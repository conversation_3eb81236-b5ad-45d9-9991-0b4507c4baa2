<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Calculator Pro - Test Page</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>SEO Calculator Pro - Test Suite</h1>
            <p class="lead">Verify functionality and components</p>
        </div>
    </div>

    <div class="container mb-5">
        <div class="card">
            <div class="card-body">
                <h3>Test Cases</h3>

                <!-- Content Analysis Test -->
                <div class="test-section mb-4">
                    <h4>1. Content Analysis Test</h4>
                    <button class="btn btn-primary mb-3" onclick="runContentTest()">Run Content Test</button>
                    <div id="contentTestResults" class="result-card" style="display: none;"></div>
                </div>

                <!-- Keyword Density Test -->
                <div class="test-section mb-4">
                    <h4>2. Keyword Density Test</h4>
                    <button class="btn btn-primary mb-3" onclick="runKeywordTest()">Run Keyword Test</button>
                    <div id="keywordTestResults" class="result-card" style="display: none;"></div>
                </div>

                <!-- Meta Tags Test -->
                <div class="test-section mb-4">
                    <h4>3. Meta Tags Test</h4>
                    <button class="btn btn-primary mb-3" onclick="runMetaTest()">Run Meta Tags Test</button>
                    <div id="metaTestResults" class="result-card" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        // Test Data
        const testContent = `
            Search Engine Optimization (SEO) is crucial for online visibility.
            It helps websites rank better in search results and attract more visitors.
            Good SEO practices include quality content, relevant keywords, and proper meta tags.
            This is a sample text for testing the content analysis functionality.
        `;

        const testKeyword = "SEO";
        const testTitle = "Complete Guide to Search Engine Optimization | SEO Tips";
        const testDescription = "Learn essential SEO techniques and best practices to improve your website's visibility in search engines. Get expert tips and strategies.";

        // Test Functions
        function runContentTest() {
            const results = document.getElementById('contentTestResults');
            results.style.display = 'block';
            results.innerHTML = '<h5>Content Analysis Results:</h5>';

            // Test word count
            const wordCount = getWordCount(testContent);
            results.innerHTML += `<p>Word Count: ${wordCount} words ✓</p>`;

            // Test reading time
            const readingTime = calculateReadingTime(wordCount);
            results.innerHTML += `<p>Reading Time: ${readingTime} min ✓</p>`;

            // Test readability
            const readability = calculateReadabilityScore(testContent);
            results.innerHTML += `<p>Readability Score: ${readability} ✓</p>`;

            // Test SEO score
            const seoScore = calculateSEOScore(testContent);
            results.innerHTML += `<p>SEO Score: ${seoScore}% ✓</p>`;
        }

        function runKeywordTest() {
            const results = document.getElementById('keywordTestResults');
            results.style.display = 'block';
            results.innerHTML = '<h5>Keyword Density Results:</h5>';

            const keywordResults = calculateKeywordDensity(testContent, testKeyword);
            results.innerHTML += `
                <p>Keyword Count: ${keywordResults.count} ✓</p>
                <p>Keyword Density: ${keywordResults.density}% ✓</p>
                <p>Optimization Status: ${keywordResults.isOptimal ? 'Optimal' : 'Needs Improvement'} ✓</p>
            `;
        }

        function runMetaTest() {
            const results = document.getElementById('metaTestResults');
            results.style.display = 'block';
            results.innerHTML = '<h5>Meta Tags Results:</h5>';

            const titleAnalysis = analyzeMetaTitle(testTitle);
            const descriptionAnalysis = analyzeMetaDescription(testDescription);

            results.innerHTML += `
                <p><strong>Title Analysis:</strong></p>
                <p>Length: ${titleAnalysis.length} characters ✓</p>
                <p>Score: ${titleAnalysis.score}% ✓</p>
                <p>Message: ${titleAnalysis.message} ✓</p>
                <br>
                <p><strong>Description Analysis:</strong></p>
                <p>Length: ${descriptionAnalysis.length} characters ✓</p>
                <p>Score: ${descriptionAnalysis.score}% ✓</p>
                <p>Message: ${descriptionAnalysis.message} ✓</p>
            `;
        }
    </script>
</body>
</html>