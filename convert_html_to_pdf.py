from playwright.sync_api import sync_playwright
import os

def convert_html_files_to_pdf(directory):
    print(f"Starting conversion in directory: {directory}")
    
    with sync_playwright() as p:
        browser = p.chromium.launch()
        
        for i in range(1, 28):
            # Handle the first file (messages.html) and subsequent files (messages2.html, etc.)
            html_file = os.path.join(directory, f"messages{i if i > 1 else ''}.html")
            pdf_file = os.path.join(directory, f"messages{i if i > 1 else ''}.pdf")
            
            try:
                if os.path.exists(html_file):
                    print(f"Converting {html_file} to {pdf_file}")
                    page = browser.new_page()
                    page.goto(f"file://{html_file}")
                    page.pdf(path=pdf_file)
                    page.close()
                    print(f"Successfully converted {html_file}")
                else:
                    print(f"File not found: {html_file}")
            except Exception as e:
                print(f"Error converting {html_file}: {str(e)}")
        
        browser.close()

if __name__ == "__main__":
    directory = r"C:\Users\<USER>\Downloads\Telegram Desktop\ChatExport_2025-06-16"
    convert_html_files_to_pdf(directory)
    print("Conversion process completed!")
