# 🔄 Telegram Chat Converter

A beautiful GUI application that converts Telegram HTML chat exports to PDF or TXT format with perfect content preservation and proper chronological ordering.

## ✨ Features

- **Beautiful Dark Theme GUI** - Modern, user-friendly interface
- **Perfect Content Preservation** - No text loss, all messages preserved exactly as they are
- **Chronological Ordering** - Processes files in correct order: messages.html → messages2.html → messages3.html...
- **Bulk Conversion** - Convert multiple files at once
- **Combine Files** - Option to merge all conversations into one document
- **Multiple Formats** - Export to PDF (recommended) or TXT
- **Progress Tracking** - Real-time conversion progress
- **Smart Sorting** - Automatically handles Telegram's file naming pattern

## 🚀 Quick Start

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the Application**
   ```bash
   python html_converter.py
   ```

3. **Convert Your Files**
   - Select your HTML files or folder containing Telegram exports
   - Choose output format (PDF recommended)
   - Select output directory
   - Click "Start Conversion"

## 📁 How to Get Telegram HTML Exports

1. Open Telegram Desktop
2. Go to Settings → Advanced → Export Telegram Data
3. Select "Personal chats" and choose HTML format
4. Export to a folder
5. Use this tool to convert the exported HTML files

## 🎯 Perfect for Your Use Case

This tool is specifically designed for Telegram chat exports like yours:
- `messages.html` (first file)
- `messages2.html` 
- `messages3.html`
- ... and so on

The application automatically sorts and processes them in the correct chronological order, ensuring your chat history flows naturally from oldest to newest messages.

## 💡 Tips

- **PDF Format**: Recommended for better formatting and readability
- **Combine Files**: Creates a single document with all conversations
- **File Order**: Files are automatically sorted numerically (messages.html, messages2.html, etc.)
- **Content Preservation**: All text, emojis, timestamps, and message structure preserved

## 🔧 Requirements

- Python 3.7+
- tkinter (usually included with Python)
- beautifulsoup4
- reportlab (for PDF conversion)
- PyPDF2 (for PDF combining)

## 📝 Output Examples

### TXT Format
```
Chat: Monisha
==================================================

[26.05.2025 00:59] Groove:
Hi didi

[26.05.2025 00:59] Monisha:
Hello bhaiya
```

### PDF Format
Beautiful formatted PDF with proper styling, timestamps, and message structure preserved.

## 🎨 Interface Preview

The application features:
- 🗂️ File selection with drag-and-drop support
- 📄 Format selection (PDF/TXT)
- 💾 Output directory selection
- ⚙️ Conversion options
- 📊 Real-time progress tracking
- 🚀 One-click conversion

## 🆘 Support

If you encounter any issues:
1. Make sure all dependencies are installed
2. Check that your HTML files are valid Telegram exports
3. Ensure you have write permissions to the output directory
4. Use the built-in Help button for more information

---

**Made with ❤️ for preserving your precious chat memories!**