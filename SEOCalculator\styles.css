/* SEO Calculator Pro - Styles */

:root {
    /* Light Theme */
    --primary-color: #4a90e2;
    --secondary-color: #f8f9fa;
    --accent-color: #28a745;
    --text-color: #2c3e50;
    --bg-color: #ffffff;
    --card-bg: #ffffff;
    --border-color: #e9ecef;
    --header-gradient: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    --shadow-color: rgba(0, 0, 0, 0.1);
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --light-text: #6c757d;
    --border-radius: 15px;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] {
    --primary-color: #61a9f8;
    --secondary-color: #2c3e50;
    --accent-color: #2ecc71;
    --text-color: #ecf0f1;
    --bg-color: #1a1a1a;
    --card-bg: #2c3e50;
    --border-color: #34495e;
    --header-gradient: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    --shadow-color: rgba(0, 0, 0, 0.3);
    --danger-color: #ff4757;
    --warning-color: #ffa502;
    --light-text: #95a5a6;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background: var(--bg-color);
    min-height: 100vh;
    transition: all 0.3s ease;
}

/* Header Styles */
.header {
    background: var(--header-gradient);
    color: white;
    padding: 4rem 0;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 16px var(--shadow-color);
}

.header h1 {
    font-weight: 600;
    letter-spacing: -0.5px;
}

.header .lead {
    font-size: 1.25rem;
    opacity: 0.9;
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 8px 16px var(--shadow-color);
    transition: all 0.3s ease;
    background: var(--card-bg);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* Tab Styles */
.nav-tabs {
    border: none;
    margin-bottom: 1.5rem;
}

.nav-tabs .nav-link {
    color: var(--text-color);
    border: none;
    padding: 1rem 1.5rem;
    transition: all 0.3s ease;
    border-radius: var(--border-radius);
    margin-right: 0.5rem;
}

.nav-tabs .nav-link:hover {
    background: var(--secondary-color);
    color: var(--primary-color);
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background: rgba(74, 144, 226, 0.1);
    border-bottom: 3px solid var(--primary-color);
    font-weight: 600;
}

/* Form Controls */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

textarea.form-control {
    min-height: 120px;
}

/* Buttons */
.btn {
    border-radius: 30px;
    padding: 0.8rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: var(--primary-color);
    border: none;
    box-shadow: 0 4px 6px rgba(74, 144, 226, 0.2);
}

.btn-primary:hover {
    background: #357abd;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(74, 144, 226, 0.3);
}

/* Results Section */
.result-card {
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.feature-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(74, 144, 226, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.feature-icon:hover {
    transform: scale(1.1);
    background: rgba(74, 144, 226, 0.2);
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
    margin-bottom: 1rem;
}

.progress-bar {
    border-radius: 4px;
    transition: width 0.6s ease;
}

/* Score Circle */
.score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 1rem auto;
    position: relative;
    box-shadow: inset 0 0 0 8px rgba(74, 144, 226, 0.1);
}

.score-circle::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border-radius: 50%;
    border: 8px solid transparent;
    border-top-color: var(--primary-color);
    border-right-color: var(--primary-color);
    transform: rotate(var(--percentage, 0deg));
    transition: transform 1s ease;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.6s ease forwards;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.highlight {
    animation: pulse 2s infinite;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .header {
        padding: 2rem 0;
    }

    .nav-tabs .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .score-circle {
        width: 100px;
        height: 100px;
        font-size: 1.5rem;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 3px solid rgba(74, 144, 226, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
    margin: 1rem auto;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Tool Tips */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 10px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}