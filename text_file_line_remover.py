import os
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
import customtkinter

class TextLineRemoverApp:
    def __init__(self, master):
        self.master = master
        master.title("Pro Text File Line Remover")
        master.geometry("1000x750") # Slightly larger for better spacing
        master.resizable(True, True)

        # Set CustomTkinter appearance mode and color theme
        customtkinter.set_appearance_mode("System")  # Modes: "System" (default), "Dark", "Light"
        customtkinter.set_default_color_theme("blue")  # Themes: "blue" (default), "dark-blue", "green"

        self.directory_path = ""
        self.txt_files = []
        self.current_filepath = ""

        # Configure grid weights for main window
        master.grid_rowconfigure(1, weight=1) # Row for main content frame
        master.grid_columnconfigure(0, weight=1)

        # --- Main Container Frame (using grid for overall layout) ---
        self.main_container_frame = customtkinter.CTkFrame(master)
        self.main_container_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        self.main_container_frame.grid_columnconfigure(0, weight=1) # Allow this frame to expand

        # --- Directory Selection ---
        self.dir_frame = customtkinter.CTkFrame(self.main_container_frame)
        self.dir_frame.grid(row=0, column=0, padx=10, pady=10, sticky="ew")
        self.dir_frame.grid_columnconfigure(1, weight=1) # Make entry expand

        self.dir_label = customtkinter.CTkLabel(self.dir_frame, text="1. Select Directory:")
        self.dir_label.grid(row=0, column=0, padx=(10, 0), pady=10, sticky="w")

        self.dir_entry = customtkinter.CTkEntry(self.dir_frame, placeholder_text="No directory selected", state="readonly")
        self.dir_entry.grid(row=0, column=1, padx=10, pady=10, sticky="ew")

        self.browse_button = customtkinter.CTkButton(self.dir_frame, text="Browse", command=self.browse_directory)
        self.browse_button.grid(row=0, column=2, padx=(0, 10), pady=10, sticky="e")

        # --- File List and Content ---
        self.content_area_frame = customtkinter.CTkFrame(self.main_container_frame)
        self.content_area_frame.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")
        self.content_area_frame.grid_columnconfigure(1, weight=1) # Content text area column
        self.content_area_frame.grid_rowconfigure(0, weight=1) # Both listbox and text area rows

        # File List Sub-frame
        self.file_list_sub_frame = customtkinter.CTkFrame(self.content_area_frame)
        self.file_list_sub_frame.grid(row=0, column=0, padx=(0, 10), pady=0, sticky="nsew")
        self.file_list_sub_frame.grid_rowconfigure(1, weight=1) # Listbox row

        self.file_list_label = customtkinter.CTkLabel(self.file_list_sub_frame, text="2. Text Files:")
        self.file_list_label.grid(row=0, column=0, padx=10, pady=(10, 5), sticky="w")

        self.file_listbox = tk.Listbox(self.file_list_sub_frame, width=35, height=20, selectmode=tk.SINGLE,
                                       font=('Segoe UI', 10),
                                       bg=customtkinter.ThemeManager.theme["CTkFrame"]["fg_color"][0],
                                       fg=customtkinter.ThemeManager.theme["CTkLabel"]["text_color"][0],
                                       selectbackground=customtkinter.ThemeManager.theme["CTkButton"]["fg_color"][0],
                                       selectforeground=customtkinter.ThemeManager.theme["CTkButton"]["text_color"][0],
                                       borderwidth=0, highlightthickness=0) # Remove default border
        self.file_listbox.grid(row=1, column=0, padx=10, pady=5, sticky="nsew")
        self.file_listbox.bind('<<ListboxSelect>>', self.load_selected_file)

        self.file_list_scrollbar = customtkinter.CTkScrollbar(self.file_list_sub_frame, command=self.file_listbox.yview)
        self.file_list_scrollbar.grid(row=1, column=1, sticky="ns", pady=5)
        self.file_listbox.config(yscrollcommand=self.file_list_scrollbar.set)

        # File Content Sub-frame
        self.file_content_sub_frame = customtkinter.CTkFrame(self.content_area_frame)
        self.file_content_sub_frame.grid(row=0, column=1, padx=0, pady=0, sticky="nsew")
        self.file_content_sub_frame.grid_rowconfigure(1, weight=1) # Text area row

        self.content_label = customtkinter.CTkLabel(self.file_content_sub_frame, text="3. File Content (Edit Here):")
        self.content_label.grid(row=0, column=0, padx=10, pady=(10, 5), sticky="w")

        self.content_text = scrolledtext.ScrolledText(self.file_content_sub_frame, wrap=tk.WORD,
                                                     font=('Consolas', 10), undo=True,
                                                     bg=customtkinter.ThemeManager.theme["CTkFrame"]["fg_color"][0],
                                                     fg=customtkinter.ThemeManager.theme["CTkLabel"]["text_color"][0],
                                                     insertbackground=customtkinter.ThemeManager.theme["CTkLabel"]["text_color"][0],
                                                     borderwidth=0, highlightthickness=0) # Remove default border
        self.content_text.grid(row=1, column=0, padx=10, pady=5, sticky="nsew")

        # --- Line Removal and Actions ---
        self.action_frame = customtkinter.CTkFrame(self.main_container_frame)
        self.action_frame.grid(row=2, column=0, padx=10, pady=10, sticky="ew")
        self.action_frame.grid_columnconfigure(1, weight=1) # Make entry expand

        self.line_to_remove_label = customtkinter.CTkLabel(self.action_frame, text="4. Line to Remove (exact match):")
        self.line_to_remove_label.grid(row=0, column=0, padx=(10, 0), pady=10, sticky="w")

        self.line_to_remove_entry = customtkinter.CTkEntry(self.action_frame, placeholder_text="Enter line to remove")
        self.line_to_remove_entry.grid(row=0, column=1, padx=10, pady=10, sticky="ew")

        self.remove_button = customtkinter.CTkButton(self.action_frame, text="Remove Line from Display", command=self.remove_line)
        self.remove_button.grid(row=0, column=2, padx=5, pady=10, sticky="e")

        self.save_button = customtkinter.CTkButton(self.action_frame, text="Save Current File", command=self.save_current_file)
        self.save_button.grid(row=0, column=3, padx=(0, 10), pady=10, sticky="e")

        # --- Combine Files Button ---
        self.combine_frame = customtkinter.CTkFrame(self.main_container_frame)
        self.combine_frame.grid(row=3, column=0, padx=10, pady=10, sticky="ew")
        self.combine_frame.grid_columnconfigure(0, weight=1) # Center button

        self.combine_button = customtkinter.CTkButton(self.combine_frame, text="5. Combine All .txt Files in Directory", command=self.combine_all_files)
        self.combine_button.grid(row=0, column=0, pady=10)

        # --- Status Bar ---
        self.status_label = customtkinter.CTkLabel(master, text="Ready", fg_color=("gray80", "gray20"),
                                                   text_color=("black", "white"), corner_radius=0)
        self.status_label.grid(row=4, column=0, padx=0, pady=0, sticky="ew", columnspan=1) # Place in grid, span full width
        master.grid_rowconfigure(4, weight=0) # Status bar row should not expand

    def update_status(self, message):
        self.status_label.configure(text=message)
        self.master.update_idletasks() # Ensure status updates immediately

    def browse_directory(self):
        directory = filedialog.askdirectory()
        if directory:
            self.directory_path = directory
            self.dir_entry.configure(state="normal")
            self.dir_entry.delete(0, tk.END)
            self.dir_entry.insert(0, self.directory_path)
            self.dir_entry.configure(state="readonly")
            self.load_txt_files()
            self.update_status(f"Directory selected: {self.directory_path}")
            self.content_text.delete(1.0, tk.END) # Clear content when new directory selected
            self.current_filepath = "" # Reset current file

    def load_txt_files(self):
        self.file_listbox.delete(0, tk.END)
        self.txt_files = []
        if self.directory_path and os.path.isdir(self.directory_path):
            for f in os.listdir(self.directory_path):
                if f.endswith('.txt') and os.path.isfile(os.path.join(self.directory_path, f)):
                    self.txt_files.append(f)
                    self.file_listbox.insert(tk.END, f)
            if not self.txt_files:
                self.update_status("No .txt files found in the selected directory.")
            else:
                self.update_status(f"Found {len(self.txt_files)} .txt files.")
        else:
            self.update_status("Please select a valid directory.")

    def load_selected_file(self, event=None):
        selected_index = self.file_listbox.curselection()
        if selected_index:
            filename = self.file_listbox.get(selected_index[0])
            self.current_filepath = os.path.join(self.directory_path, filename)
            try:
                with open(self.current_filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.content_text.delete(1.0, tk.END)
                self.content_text.insert(tk.END, content)
                self.update_status(f"Loaded: {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Could not read file {filename}: {e}")
                self.update_status(f"Error loading {filename}")
        else:
            self.update_status("No file selected.")

    def remove_line(self):
        if not self.current_filepath:
            messagebox.showwarning("Warning", "Please select a file first.")
            return

        line_to_remove = self.line_to_remove_entry.get()
        if not line_to_remove:
            messagebox.showwarning("Warning", "Please enter a line to remove.")
            return

        current_content = self.content_text.get(1.0, tk.END)
        lines = current_content.splitlines(keepends=True) # Keep line endings for accurate saving

        # Normalize both the line from the file and the input line by removing all whitespace
        normalized_line_to_remove = "".join(line_to_remove.split())
        modified_lines = [line for line in lines if "".join(line.split()) != normalized_line_to_remove]
        
        self.content_text.delete(1.0, tk.END)
        self.content_text.insert(tk.END, "".join(modified_lines))
        self.update_status(f"Line '{line_to_remove.strip()}' removed from display. Click 'Save Current File' to apply changes.")
        self.line_to_remove_entry.delete(0, tk.END) # Clear the entry after removal

    def save_current_file(self):
        if not self.current_filepath:
            messagebox.showwarning("Warning", "No file selected to save.")
            return
        
        try:
            content_to_save = self.content_text.get(1.0, tk.END).strip() # Get content and remove trailing newline
            
            # Process lines: remove internal spaces and filter out empty lines
            processed_lines = []
            for line in content_to_save.splitlines():
                cleaned_line = "".join(line.split()) # Remove all internal spaces
                if cleaned_line: # Only add if not empty after cleaning
                    processed_lines.append(cleaned_line)
            
            with open(self.current_filepath, 'w', encoding='utf-8') as f:
                f.write("\n".join(processed_lines) + "\n") # Write back with single newlines, ensure file ends with one newline
            
            messagebox.showinfo("Success", f"File saved successfully: {os.path.basename(self.current_filepath)}")
            self.update_status(f"Saved: {os.path.basename(self.current_filepath)}")
        except Exception as e:
            messagebox.showerror("Error", f"Could not save file {os.path.basename(self.current_filepath)}: {e}")
            self.update_status(f"Error saving {os.path.basename(self.current_filepath)}")

    def combine_all_files(self):
        if not self.directory_path:
            messagebox.showwarning("Warning", "Please select a directory first.")
            return
        
        if not self.txt_files:
            messagebox.showwarning("Warning", "No .txt files found to combine in the selected directory.")
            return

        output_combined_filepath = os.path.join(self.directory_path, "combined_output.txt")
        
        try:
            all_combined_content = []
            for filename in self.txt_files:
                filepath = os.path.join(self.directory_path, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as infile:
                        file_content = infile.read().strip() # Read content and strip leading/trailing whitespace
                        if file_content: # Only add if content is not empty
                            all_combined_content.append(file_content)
                    self.update_status(f"Appended {filename} to {os.path.basename(output_combined_filepath)}")
                except Exception as e:
                    self.update_status(f"Skipping {filename} due to error: {e}")
                    continue # Continue to next file even if one fails
            
            with open(output_combined_filepath, 'w', encoding='utf-8') as outfile:
                outfile.write("\n".join(all_combined_content)) # Join with single newlines
            
            messagebox.showinfo("Success", f"All files combined into {output_combined_filepath}")
            self.update_status(f"All files combined into {os.path.basename(output_combined_filepath)}")
        except Exception as e:
            messagebox.showerror("Error", f"Error combining files: {e}")
            self.update_status("Error combining files.")

if __name__ == "__main__":
    root = customtkinter.CTk()
    app = TextLineRemoverApp(root)
    root.mainloop()
