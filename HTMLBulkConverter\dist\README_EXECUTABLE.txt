🔄 HTML Bulk Converter - Executable Version
=============================================

📁 What's in this folder:
• HTMLBulkConverter.exe - The main application (29.1 MB)
• Run_HTMLConverter.bat - Batch file to run the application
• README_EXECUTABLE.txt - This file

🚀 How to run:
1. Double-click "HTMLBulkConverter.exe" 
   OR
2. Double-click "Run_HTMLConverter.bat"

✨ Features:
• Convert bulk HTML files to PDF or TXT
• Beautiful dark theme interface
• Smart numerical sorting (messages.html → messages2.html → etc.)
• Combine multiple files into one document
• Perfect content preservation
• No Python installation required!

📝 How to use:
1. Click "Select Folder" and choose your HTML files folder
2. Choose output format (PDF recommended)
3. Select output directory
4. Check "Combine files" if you want one document
5. Click "Start Conversion"

💡 Perfect for:
• Telegram chat exports
• WhatsApp chat exports
• Documentation files
• Any HTML content

🔧 System Requirements:
• Windows 7/8/10/11
• No additional software needed
• Works on any Windows computer

📞 Support:
If you encounter any issues:
• Make sure you have write permissions to the output folder
• Check that your HTML files are valid
• Try running as administrator if needed

🎯 File Size: 29.1 MB (includes all dependencies)
🔒 No internet connection required
✅ Virus-free standalone executable

Made with ❤️ for converting your HTML content beautifully!