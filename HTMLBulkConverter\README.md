
# HTML Bulk Converter

A beautiful GUI application that converts multiple HTML files to PDF or TXT format and can combine them into a single file.

## Features

- Convert multiple HTML files to PDF or TXT format
- Modern and user-friendly interface
- Option to combine all converted files into one
- Progress tracking
- Support for batch processing
- Error handling and notifications

## Installation

1. Make sure you have Python 3.7 or higher installed on your system
2. Install wkhtmltopdf (required for PDF conversion):
   - Download from: https://wkhtmltopdf.org/downloads.html
   - Add the installation directory to your system PATH

3. Double-click `start_converter.bat` to install dependencies and start the application
   - This will automatically install required Python packages
   - Launch the application

## How to Use

1. Launch the application by running `start_converter.bat`
2. Click "Browse" to select the folder containing your HTML files
3. Choose output format (PDF or TXT)
4. Check "Combine all files" if you want a single output file
5. Click "Convert Files" to start the conversion
6. Monitor progress in the progress bar
7. Check the output folder for converted files

## Output Formats

### PDF Output
- Maintains formatting and layout
- Includes images and styling
- Perfect for archiving or sharing

### TXT Output
- Extracts plain text content
- Removes HTML formatting
- Ideal for text analysis or editing

## Tips

- For large HTML files, PDF conversion might take longer
- Make sure you have write permissions in the output folder
- Close any open output files before conversion
- Regular backups of source files are recommended

## Troubleshooting

1. If PDF conversion fails:
   - Verify wkhtmltopdf is properly installed
   - Check if the HTML files are valid
   - Ensure you have sufficient disk space

2. If combining files fails:
   - Check if you have write permissions
   - Ensure no output files are open
   - Verify source files are readable

3. If the application won't start:
   - Check if Python is properly installed
   - Verify all dependencies are installed
   - Check the console for error messages

## Requirements

- Python 3.7+
- wkhtmltopdf
- beautifulsoup4
- pdfkit
- ttkthemes
- Pillow

## Support

If you encounter any issues or need assistance:
1. Check the troubleshooting section
2. Verify all installation steps were followed
3. Check console output for error messages

## License

This software is free to use and modify. Please credit the original author if you redistribute or modify it.
