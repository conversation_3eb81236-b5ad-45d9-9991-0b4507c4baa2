from flask import Flask, render_template, request, redirect, url_for, send_file, flash
import os
import threading
from playwright.sync_api import sync_playwright
from PyPDF2 import PdfMerger
import tempfile
import zipfile
import shutil

app = Flask(__name__)
app.secret_key = 'supersecretkey'

def convert_html_to_pdf_and_merge(folder_path, output_path, status_callback=None):
    pdf_files = []
    with sync_playwright() as p:
        browser = p.chromium.launch()
        for file_name in sorted(os.listdir(folder_path)):
            if file_name.lower().endswith('.html'):
                html_file = os.path.join(folder_path, file_name)
                pdf_file = os.path.join(folder_path, file_name.rsplit('.', 1)[0] + '.pdf')
                if status_callback:
                    status_callback(f"Converting {html_file}...")
                page = browser.new_page()
                page.goto(f"file://{html_file}")
                page.pdf(path=pdf_file)
                page.close()
                pdf_files.append(pdf_file)
        browser.close()

    if status_callback:
        status_callback("Merging PDFs...")
    merger = PdfMerger()
    for pdf in pdf_files:
        merger.append(pdf)
    merger.write(output_path)
    merger.close()
    if status_callback:
        status_callback(f"Conversion and merge completed! Output: {output_path}")

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        files = request.files.getlist('files')
        if not files or files[0].filename == '':
            flash('No files selected')
            return redirect(request.url)

        # Save uploaded files temporarily
        temp_dir = tempfile.mkdtemp()
        folder_path = os.path.join(temp_dir, 'uploaded')
        os.makedirs(folder_path, exist_ok=True)

        # Save all uploaded HTML files
        for file in files:
            if file.filename.lower().endswith('.html'):
                file_path = os.path.join(folder_path, file.filename)
                file.save(file_path)

        output_pdf = os.path.join(temp_dir, 'merged_output.pdf')

        # Run conversion in a thread to avoid blocking
        thread = threading.Thread(target=convert_html_to_pdf_and_merge, args=(folder_path, output_pdf))
        thread.start()
        thread.join()

        return send_file(output_pdf, as_attachment=True, download_name='merged_output.pdf')

    return render_template('index.html')

if __name__ == '__main__':
    app.run(debug=True)
