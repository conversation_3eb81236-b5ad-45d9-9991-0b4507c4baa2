@echo off
title HTML Bulk Converter - Installer
color 0B
cls

echo ========================================
echo  HTML Bulk Converter - Installer
echo ========================================
echo.

echo This will install HTML Bulk Converter to your desired location.
echo.

set /p "install_path=Enter installation path (or press Enter for C:\HTMLBulkConverter): "

if "%install_path%"=="" set "install_path=C:\HTMLBulkConverter"

echo.
echo Installing to: %install_path%
echo.

:: Create installation directory
if not exist "%install_path%" (
    mkdir "%install_path%"
    if errorlevel 1 (
        echo Error: Could not create directory. Try running as administrator.
        pause
        exit /b 1
    )
)

:: Copy files
echo Copying files...
copy "HTMLBulkConverter.exe" "%install_path%\" >nul
copy "Run_HTMLConverter.bat" "%install_path%\" >nul
copy "README_EXECUTABLE.txt" "%install_path%\" >nul

if errorlevel 1 (
    echo Error: Could not copy files. Try running as administrator.
    pause
    exit /b 1
)

echo.
echo ✅ Installation completed successfully!
echo.
echo 📁 Installed to: %install_path%
echo.
echo 🚀 To run the application:
echo    • Go to %install_path%
echo    • Double-click HTMLBulkConverter.exe
echo.

set /p "create_shortcut=Create desktop shortcut? (y/n): "

if /i "%create_shortcut%"=="y" (
    echo Creating desktop shortcut...
    
    :: Create VBS script to create shortcut
    echo Set oWS = WScript.CreateObject("WScript.Shell") > "%temp%\CreateShortcut.vbs"
    echo sLinkFile = "%USERPROFILE%\Desktop\HTML Bulk Converter.lnk" >> "%temp%\CreateShortcut.vbs"
    echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%temp%\CreateShortcut.vbs"
    echo oLink.TargetPath = "%install_path%\HTMLBulkConverter.exe" >> "%temp%\CreateShortcut.vbs"
    echo oLink.WorkingDirectory = "%install_path%" >> "%temp%\CreateShortcut.vbs"
    echo oLink.Description = "HTML Bulk Converter - Convert HTML files to PDF/TXT" >> "%temp%\CreateShortcut.vbs"
    echo oLink.Save >> "%temp%\CreateShortcut.vbs"
    
    cscript "%temp%\CreateShortcut.vbs" >nul
    del "%temp%\CreateShortcut.vbs"
    
    echo ✅ Desktop shortcut created!
)

echo.
echo Installation complete! Enjoy using HTML Bulk Converter!
echo.
pause