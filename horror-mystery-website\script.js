// 🔥 ULTIMATE HORROR JAVASCRIPT - THE NIGHTMARE ENGINE 🔥

// Global variables
let completedPuzzles = new Set();
let currentPuzzle = null;
let horrorSounds = {
    ambient: null,
    scream: null
};

// Initialize the nightmare
document.addEventListener('DOMContentLoaded', function() {
    initializeHorrorEffects();
    loadHorrorImages();
    setupEventListeners();

    // Add random horror effects
    setInterval(randomHorrorEffect, 15000);
});

// Initialize horror effects
function initializeHorrorEffects() {
    // Setup audio
    horrorSounds.ambient = document.getElementById('ambientAudio');
    horrorSounds.scream = document.getElementById('screamAudio');

    // Add random screen flashes
    setInterval(screenFlash, Math.random() * 30000 + 10000);

    // Add random cursor changes
    setInterval(changeCursor, Math.random() * 20000 + 5000);

    console.log('🔥 THE NIGHTMARE HAS BEGUN 🔥');
}

// Load clear, visible horror images that match themes
function loadHorrorImages() {
    // Simple, direct approach - use custom SVGs that are guaranteed to work and be visible
    const puzzleTypes = ['ocean', 'haunted', 'graveyard', 'hospital', 'forest', 'mirror', 'basement', 'ritual', 'zombie', 'final'];

    puzzleTypes.forEach(key => {
        const img = document.getElementById(key + 'Img');
        if (img) {
            // Use custom SVG that matches the theme and is clearly visible
            img.src = createHorrorSVG(img.alt, key);
            img.style.filter = 'none'; // No filters - keep images clear and visible
            img.style.opacity = '1';
            img.style.border = '3px solid #ff0000';
            img.style.borderRadius = '10px';
            img.style.boxShadow = '0 0 15px #ff0000';
        }
    });

    return; // Skip the complex image loading for now

    // AI-generated horror image prompts for each theme
    const horrorImagePrompts = {
        ocean: "terrifying deep sea monster with glowing red eyes emerging from dark ocean depths, tentacles, underwater horror, photorealistic, dark atmosphere, 4k",
        haunted: "abandoned haunted house at night, broken windows, ghostly figures, fog, eerie lighting, horror atmosphere, photorealistic, very scary",
        graveyard: "spooky cemetery at midnight, old tombstones, fog, glowing spirits, dark trees, horror scene, photorealistic, frightening",
        hospital: "abandoned hospital corridor, flickering lights, blood on walls, medical equipment, decay, horror atmosphere, photorealistic, terrifying",
        forest: "dark evil forest at night, twisted trees, witch's lair, glowing eyes in darkness, supernatural horror, photorealistic, very scary",
        mirror: "cursed antique mirror with demonic reflection, ornate frame, dark room, supernatural horror, photorealistic, terrifying",
        basement: "torture chamber basement, chains, blood, dungeon, horror scene, dark lighting, photorealistic, extremely frightening",
        ritual: "demonic ritual circle, pentagram, candles, dark symbols, satanic horror, fire, photorealistic, very dark and scary",
        zombie: "zombie apocalypse scene, undead horde, decay, post-apocalyptic, horror atmosphere, photorealistic, terrifying",
        final: "ultimate nightmare demon boss, fire, darkness, hell, final horror boss, photorealistic, extremely terrifying"
    };

    // REAL HORROR IMAGES - Directly use AI-generated horror content
    const horrorImages = {
        ocean: [
            "https://image.pollinations.ai/prompt/terrifying%20deep%20sea%20monster%20kraken%20tentacles%20emerging%20from%20dark%20ocean%20abyss%20glowing%20red%20eyes%20underwater%20horror%20photorealistic%20very%20scary?width=500&height=400&model=flux&enhance=true&nologo=true",
            "https://image.pollinations.ai/prompt/giant%20sea%20creature%20leviathan%20dark%20ocean%20depths%20horror%20monster%20tentacles%20scary%20underwater%20nightmare?width=500&height=400&model=flux&enhance=true&nologo=true",
            "https://image.pollinations.ai/prompt/ocean%20horror%20deep%20sea%20monster%20abyss%20tentacles%20dark%20water%20scary%20creature?width=500&height=400&model=flux&enhance=true&nologo=true"
        ],
        haunted: [
            "https://cdn.pixabay.com/photo/2017/10/10/21/47/house-2837353_960_720.jpg",
            "https://cdn.pixabay.com/photo/2016/11/29/05/45/abandoned-1867330_960_720.jpg",
            "https://images.unsplash.com/photo-1520637736862-4d197d17c93a?w=500&h=400&fit=crop&auto=format&q=80",
            "https://image.pollinations.ai/prompt/haunted%20house%20scary%20night?width=500&height=400&enhance=true",
            "https://picsum.photos/500/400?random=2001"
        ],
        graveyard: [
            "https://images.unsplash.com/photo-1509248961158-d3f0b4c0b6d2?w=500&h=400&fit=crop&auto=format&q=80",
            "https://images.unsplash.com/photo-**********442-48f60103fc96?w=500&h=400&fit=crop&auto=format&q=80",
            "https://images.unsplash.com/photo-1520637736862-4d197d17c93a?w=500&h=400&fit=crop&auto=format&q=80",
            "https://images.unsplash.com/photo-**********-2a8555f1a136?w=500&h=400&fit=crop&auto=format&q=80",
            "https://images.unsplash.com/photo-1586773860418-d37222d8fce3?w=500&h=400&fit=crop&auto=format&q=80"
        ],
        hospital: [
            "https://image.pollinations.ai/prompt/abandoned%20hospital%20corridor%20flickering%20lights%20blood%20on%20walls%20medical%20equipment%20decay%20horror%20atmosphere%20very%20terrifying%20photorealistic?width=500&height=400&model=flux&enhance=true&nologo=true",
            "https://image.pollinations.ai/prompt/creepy%20hospital%20hallway%20medical%20horror%20abandoned%20scary%20blood%20dark%20nightmare?width=500&height=400&model=flux&enhance=true&nologo=true",
            "https://image.pollinations.ai/prompt/hospital%20horror%20medical%20equipment%20abandoned%20scary%20corridor%20blood%20terror?width=500&height=400&model=flux&enhance=true&nologo=true"
        ],
        forest: [
            "https://image.pollinations.ai/prompt/dark%20evil%20forest%20at%20night%20twisted%20trees%20witch%20lair%20glowing%20eyes%20in%20darkness%20supernatural%20horror%20very%20scary%20photorealistic?width=500&height=400&model=flux&enhance=true&nologo=true",
            "https://image.pollinations.ai/prompt/scary%20dark%20forest%20witch%20trees%20night%20horror%20supernatural%20evil%20spooky?width=500&height=400&model=flux&enhance=true&nologo=true",
            "https://image.pollinations.ai/prompt/forest%20horror%20dark%20trees%20witch%20supernatural%20scary%20night%20evil?width=500&height=400&model=flux&enhance=true&nologo=true"
        ],
        mirror: [
            "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=400&fit=crop&auto=format&q=80",
            "https://images.unsplash.com/photo-**********442-48f60103fc96?w=500&h=400&fit=crop&auto=format&q=80",
            "https://images.unsplash.com/photo-1520637736862-4d197d17c93a?w=500&h=400&fit=crop&auto=format&q=80",
            "https://images.unsplash.com/photo-1509248961158-d3f0b4c0b6d2?w=500&h=400&fit=crop&auto=format&q=80",
            "https://images.unsplash.com/photo-**********-2a8555f1a136?w=500&h=400&fit=crop&auto=format&q=80"
        ],
        basement: [
            "https://image.pollinations.ai/prompt/torture%20chamber%20basement%20chains%20blood%20dungeon%20horror%20scene%20dark%20lighting%20extremely%20frightening%20photorealistic?width=500&height=400&model=flux&enhance=true&nologo=true",
            "https://image.pollinations.ai/prompt/scary%20basement%20dungeon%20chains%20torture%20horror%20dark%20blood%20nightmare?width=500&height=400&model=flux&enhance=true&nologo=true",
            "https://image.pollinations.ai/prompt/basement%20horror%20dungeon%20chains%20torture%20scary%20dark%20blood%20terror?width=500&height=400&model=flux&enhance=true&nologo=true"
        ],
        ritual: [
            "https://image.pollinations.ai/prompt/demonic%20ritual%20circle%20pentagram%20candles%20dark%20symbols%20satanic%20horror%20fire%20very%20dark%20and%20scary%20photorealistic?width=500&height=400&model=flux&enhance=true&nologo=true",
            "https://image.pollinations.ai/prompt/satanic%20ritual%20pentagram%20candles%20demon%20summoning%20horror%20dark%20scary?width=500&height=400&model=flux&enhance=true&nologo=true",
            "https://image.pollinations.ai/prompt/ritual%20horror%20pentagram%20candles%20demon%20satanic%20scary%20dark%20evil?width=500&height=400&model=flux&enhance=true&nologo=true"
        ],
        zombie: [
            "https://images.unsplash.com/photo-**********442-48f60103fc96?w=500&h=400&fit=crop&auto=format&q=80",
            "https://images.unsplash.com/photo-1509248961158-d3f0b4c0b6d2?w=500&h=400&fit=crop&auto=format&q=80",
            "https://images.unsplash.com/photo-**********-2a8555f1a136?w=500&h=400&fit=crop&auto=format&q=80",
            "https://images.unsplash.com/photo-1520637736862-4d197d17c93a?w=500&h=400&fit=crop&auto=format&q=80",
            "https://images.unsplash.com/photo-1586773860418-d37222d8fce3?w=500&h=400&fit=crop&auto=format&q=80"
        ],
        final: [
            "https://image.pollinations.ai/prompt/ultimate%20nightmare%20demon%20boss%20fire%20darkness%20hell%20final%20horror%20boss%20extremely%20terrifying%20photorealistic?width=500&height=400&model=flux&enhance=true&nologo=true",
            "https://image.pollinations.ai/prompt/demon%20boss%20hell%20fire%20nightmare%20horror%20final%20boss%20scary%20evil?width=500&height=400&model=flux&enhance=true&nologo=true",
            "https://image.pollinations.ai/prompt/final%20boss%20demon%20hell%20fire%20horror%20nightmare%20scary%20ultimate?width=500&height=400&model=flux&enhance=true&nologo=true"
        ]
    };

    // Function to try loading images with fallbacks
    function tryLoadImage(img, urls, index = 0) {
        if (index >= urls.length) {
            // All URLs failed, try AI generation or use enhanced SVG fallback
            generateAIImage(img, horrorImagePrompts[img.id.replace('Img', '')]);
            return;
        }

        img.onload = function() {
            this.style.filter = 'sepia(30%) saturate(150%) brightness(1.1) contrast(1.3) hue-rotate(320deg)';
            this.style.opacity = '1';
            this.style.border = '3px solid #ff0000';
            this.style.borderRadius = '10px';
            this.style.boxShadow = '0 0 15px #ff0000';
            console.log(`✅ Loaded image for ${img.alt}`);
        };

        img.onerror = function() {
            console.log(`❌ Failed to load image ${index + 1} for ${img.alt}`);
            // Try next URL
            tryLoadImage(img, urls, index + 1);
        };

        img.style.opacity = '0.7';
        img.src = urls[index];
    }

    // Load images for each puzzle
    Object.keys(horrorImages).forEach(key => {
        const img = document.getElementById(key + 'Img');
        if (img) {
            tryLoadImage(img, horrorImages[key]);
        }
    });
}

// Generate AI images using multiple services
async function generateAIImage(img, prompt) {
    console.log(`🎨 Generating AI image for: ${prompt}`);

    // Try multiple AI image generation services
    const aiServices = [
        // Pollinations AI (Free)
        `https://image.pollinations.ai/prompt/${encodeURIComponent(prompt)}?width=500&height=400&model=flux&enhance=true`,
        // Picsum with filters as fallback
        `https://picsum.photos/500/400?random=${Math.floor(Math.random() * 1000)}`,
        // Lorem Picsum with blur effect
        `https://picsum.photos/500/400?random=${Math.floor(Math.random() * 1000)}&blur=1`
    ];

    // Try each AI service
    for (let i = 0; i < aiServices.length; i++) {
        try {
            img.onload = function() {
                // Apply horror filter to any image
                this.style.filter = 'sepia(40%) saturate(200%) brightness(0.6) contrast(1.5) hue-rotate(320deg) drop-shadow(0 0 10px #ff0000)';
                this.style.opacity = '1';
                console.log(`✅ AI image generated successfully for ${img.alt}`);
            };

            img.onerror = function() {
                console.log(`❌ AI service ${i + 1} failed for ${img.alt}`);
                if (i === aiServices.length - 1) {
                    // Last resort: use enhanced SVG
                    img.src = createHorrorSVG(img.alt, img.id.replace('Img', ''));
                    img.style.filter = 'none';
                }
            };

            img.src = aiServices[i];
            break;
        } catch (error) {
            console.log(`❌ Error with AI service ${i + 1}:`, error);
            continue;
        }
    }
}

// Create themed SVG images for each horror puzzle
function createHorrorSVG(altText, puzzleType) {
    let svg = '';

    switch(puzzleType) {
        case 'ocean':
            svg = `<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="oceanGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                        <stop offset="0%" style="stop-color:#001122;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#000033;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="100%" height="100%" fill="url(#oceanGrad)"/>
                <circle cx="200" cy="80" r="30" fill="#ff0000" opacity="0.8"/>
                <path d="M150 150 Q200 120 250 150 Q200 180 150 150" fill="#330066" stroke="#ff0000" stroke-width="2"/>
                <path d="M100 200 Q150 170 200 200 Q250 170 300 200 Q350 230 400 200" fill="none" stroke="#006666" stroke-width="3"/>
                <text x="50%" y="85%" font-family="Arial Black" font-size="24" fill="#ffffff" text-anchor="middle">OCEAN HORROR</text>
                <text x="50%" y="95%" font-family="Arial" font-size="16" fill="#cccccc" text-anchor="middle">Deep Sea Terror</text>
            </svg>`;
            break;

        case 'haunted':
            svg = `<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#000000"/>
                <rect x="150" y="100" width="100" height="120" fill="#1a1a1a" stroke="#666666" stroke-width="2"/>
                <polygon points="140,100 200,60 260,100" fill="#333333" stroke="#666666" stroke-width="2"/>
                <rect x="170" y="140" width="15" height="25" fill="#ffff00" opacity="0.7"/>
                <rect x="215" y="140" width="15" height="25" fill="#ffff00" opacity="0.7"/>
                <rect x="190" y="180" width="20" height="30" fill="#330000"/>
                <circle cx="120" cy="80" r="15" fill="#ffffff" opacity="0.6"/>
                <circle cx="280" cy="90" r="12" fill="#ffffff" opacity="0.5"/>
                <text x="50%" y="85%" font-family="Arial Black" font-size="20" fill="#ffffff" text-anchor="middle">HAUNTED HOUSE</text>
                <text x="50%" y="95%" font-family="Arial" font-size="14" fill="#cccccc" text-anchor="middle">Spirits Await</text>
            </svg>`;
            break;

        case 'graveyard':
            svg = `<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#000011"/>
                <rect x="100" y="150" width="30" height="80" fill="#333333" stroke="#666666" stroke-width="1"/>
                <rect x="180" y="140" width="35" height="90" fill="#333333" stroke="#666666" stroke-width="1"/>
                <rect x="270" y="160" width="28" height="70" fill="#333333" stroke="#666666" stroke-width="1"/>
                <circle cx="115" cy="140" r="8" fill="#ff0000" opacity="0.8"/>
                <circle cx="197" cy="130" r="10" fill="#ff0000" opacity="0.8"/>
                <circle cx="284" cy="150" r="7" fill="#ff0000" opacity="0.8"/>
                <path d="M50 250 Q200 240 350 250" fill="none" stroke="#444444" stroke-width="2"/>
                <text x="50%" y="85%" font-family="Arial Black" font-size="22" fill="#ffffff" text-anchor="middle">GRAVEYARD</text>
                <text x="50%" y="95%" font-family="Arial" font-size="14" fill="#cccccc" text-anchor="middle">Rest in Terror</text>
            </svg>`;
            break;

        case 'hospital':
            svg = `<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#001100"/>
                <rect x="50" y="80" width="300" height="140" fill="#1a1a1a" stroke="#666666" stroke-width="2"/>
                <rect x="80" y="110" width="20" height="30" fill="#ffff00" opacity="0.3"/>
                <rect x="120" y="110" width="20" height="30" fill="#ffff00" opacity="0.5"/>
                <rect x="280" y="110" width="20" height="30" fill="#ffff00" opacity="0.2"/>
                <rect x="180" y="180" width="40" height="30" fill="#330000"/>
                <path d="M190 120 L190 140 M180 130 L200 130" stroke="#ff0000" stroke-width="4"/>
                <circle cx="300" cy="60" r="20" fill="#ff0000" opacity="0.6"/>
                <text x="50%" y="85%" font-family="Arial Black" font-size="18" fill="#ffffff" text-anchor="middle">ABANDONED HOSPITAL</text>
                <text x="50%" y="95%" font-family="Arial" font-size="14" fill="#cccccc" text-anchor="middle">Medical Nightmare</text>
            </svg>`;
            break;

        case 'graveyard':
            svg = `<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#000000"/>
                <rect x="80" y="180" width="40" height="80" fill="#333333" stroke="#666666" stroke-width="2"/>
                <rect x="160" y="170" width="45" height="90" fill="#333333" stroke="#666666" stroke-width="2"/>
                <rect x="250" y="185" width="35" height="75" fill="#333333" stroke="#666666" stroke-width="2"/>
                <rect x="320" y="175" width="40" height="85" fill="#333333" stroke="#666666" stroke-width="2"/>
                <circle cx="100" cy="170" r="8" fill="#ff0000" opacity="0.8"/>
                <circle cx="182" cy="160" r="10" fill="#ff0000" opacity="0.8"/>
                <circle cx="267" cy="175" r="7" fill="#ff0000" opacity="0.8"/>
                <circle cx="340" cy="165" r="9" fill="#ff0000" opacity="0.8"/>
                <path d="M50 270 Q200 260 350 270" fill="none" stroke="#444444" stroke-width="3"/>
                <text x="50%" y="85%" font-family="Arial Black" font-size="20" fill="#ffffff" text-anchor="middle">RESURRECTION CEMETERY</text>
                <text x="50%" y="95%" font-family="Arial" font-size="14" fill="#ff0000" text-anchor="middle">The Dead Rise Tonight</text>
            </svg>`;
            break;

        case 'mirror':
            svg = `<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#000000"/>
                <rect x="120" y="50" width="160" height="200" fill="#1a1a1a" stroke="#666666" stroke-width="3"/>
                <rect x="130" y="60" width="140" height="180" fill="#000000" stroke="#333333" stroke-width="2"/>
                <circle cx="200" cy="120" r="25" fill="#ff0000" opacity="0.6"/>
                <circle cx="180" cy="140" r="15" fill="#ff0000" opacity="0.4"/>
                <circle cx="220" cy="160" r="20" fill="#ff0000" opacity="0.5"/>
                <path d="M150 100 Q200 80 250 100 Q200 120 150 100" fill="#330000" opacity="0.7"/>
                <text x="50%" y="85%" font-family="Arial Black" font-size="18" fill="#ffffff" text-anchor="middle">CURSED MIRROR</text>
                <text x="50%" y="95%" font-family="Arial" font-size="14" fill="#ff0000" text-anchor="middle">Your Soul Reflected</text>
            </svg>`;
            break;

        case 'zombie':
            svg = `<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#001100"/>
                <rect x="50" y="150" width="80" height="100" fill="#1a1a1a" stroke="#666666" stroke-width="2"/>
                <rect x="180" y="140" width="90" height="110" fill="#1a1a1a" stroke="#666666" stroke-width="2"/>
                <rect x="300" y="160" width="70" height="90" fill="#1a1a1a" stroke="#666666" stroke-width="2"/>
                <circle cx="90" cy="130" r="12" fill="#ff0000" opacity="0.8"/>
                <circle cx="225" cy="120" r="15" fill="#ff0000" opacity="0.8"/>
                <circle cx="335" cy="140" r="10" fill="#ff0000" opacity="0.8"/>
                <path d="M30 250 Q200 240 370 250" fill="none" stroke="#444444" stroke-width="3"/>
                <text x="50%" y="85%" font-family="Arial Black" font-size="18" fill="#ffffff" text-anchor="middle">ZOMBIE APOCALYPSE</text>
                <text x="50%" y="95%" font-family="Arial" font-size="14" fill="#ff0000" text-anchor="middle">The Dead Walk</text>
            </svg>`;
            break;

        default:
            // Fallback for other types
            const horrorEmojis = {
                'hospital': '🏥💉💀',
                'forest': '🌲🧙‍♀️💀',
                'basement': '🔗⛓️💀',
                'ritual': '👹🕯️💀',
                'final': '👑💀🔥'
            };

            const emoji = horrorEmojis[puzzleType] || '💀🔥💀';

            svg = `<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#000000;stop-opacity:1" />
                        <stop offset="50%" style="stop-color:#330000;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#000000;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="100%" height="100%" fill="url(#grad)"/>
                <text x="50%" y="40%" font-family="Arial Black" font-size="48" fill="#ff0000" text-anchor="middle" dy=".3em">${emoji}</text>
                <text x="50%" y="70%" font-family="Arial Black" font-size="18" fill="#ffffff" text-anchor="middle" dy=".3em">${altText.toUpperCase()}</text>
            </svg>`;
    }

    return 'data:image/svg+xml;base64,' + btoa(svg);
}

// Setup event listeners
function setupEventListeners() {
    // Puzzle card clicks
    document.querySelectorAll('.puzzle-card').forEach(card => {
        card.addEventListener('click', function() {
            const puzzleType = this.getAttribute('data-puzzle');
            openPuzzle(puzzleType);
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        const modal = document.getElementById('puzzleModal');
        if (event.target === modal) {
            closePuzzle();
        }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closePuzzle();
        }
    });
}

// Enter the nightmare realm
function enterRealm() {
    playScream();

    // Hide warning, show puzzles
    document.querySelector('.warning-section').style.display = 'none';
    document.getElementById('puzzleSelection').style.display = 'block';
    document.getElementById('progressTracker').style.display = 'block';

    // Start ambient horror sounds
    if (horrorSounds.ambient) {
        horrorSounds.ambient.volume = 0.3;
        horrorSounds.ambient.play().catch(e => console.log('Audio autoplay blocked'));
    }

    // Add dramatic entrance effect
    document.getElementById('puzzleSelection').style.opacity = '0';
    document.getElementById('puzzleSelection').style.transform = 'translateY(50px)';

    setTimeout(() => {
        document.getElementById('puzzleSelection').style.transition = 'all 1s ease';
        document.getElementById('puzzleSelection').style.opacity = '1';
        document.getElementById('puzzleSelection').style.transform = 'translateY(0)';
    }, 100);

    showHorrorMessage("Welcome to your worst nightmare... Choose your doom!");
}

// Open puzzle modal
function openPuzzle(puzzleType) {
    currentPuzzle = puzzleType;
    const modal = document.getElementById('puzzleModal');
    const content = document.getElementById('puzzleContent');

    content.innerHTML = generatePuzzleContent(puzzleType);
    modal.style.display = 'block';

    // Add entrance animation
    modal.style.opacity = '0';
    setTimeout(() => {
        modal.style.transition = 'opacity 0.5s ease';
        modal.style.opacity = '1';
    }, 10);

    playHorrorSound();
}

// Close puzzle modal
function closePuzzle() {
    const modal = document.getElementById('puzzleModal');
    modal.style.opacity = '0';
    setTimeout(() => {
        modal.style.display = 'none';
        currentPuzzle = null;
    }, 500);
}

// Generate puzzle content
function generatePuzzleContent(puzzleType) {
    const puzzles = {
        ocean: {
            title: "🌊 THE KRAKEN'S CURSE - Death from the Depths",
            story: "You're trapped in a sinking research vessel 6 miles beneath the ocean surface. Through the cracked porthole, you see a colossal tentacled beast with thousands of glowing red eyes. Its tentacles are covered in the bones of previous victims. The creature speaks in an ancient language, offering you one chance to escape before it drags you to the ocean floor to join the countless souls already trapped in its underwater graveyard. The water is rising fast, and you have minutes before the pressure crushes you alive.",
            question: "The Kraken shows you the skulls of its victims, each marked with numbers: 13, 26, 39, 52. It hisses: 'The next number in this sequence of death will be your escape code, or you'll become skull number 1,847 in my collection.' What number saves your life?",
            answer: "65",
            hint: "Each number increases by 13... the pattern of death continues..."
        },
        haunted: {
            title: "🏚️ BLACKWOOD MANOR - The Family That Never Left",
            story: "You've broken into the infamous Blackwood Manor where an entire family was brutally murdered 100 years ago. As you enter, the doors slam shut behind you. The walls begin bleeding, and you hear the screams of the murdered children echoing through the halls. Suddenly, the ghost of little Sarah Blackwood appears, her throat slit, blood dripping from her mouth. She whispers: 'Daddy killed us all with his axe. Mommy tried to hide us, but he found us. Now we're trapped here forever... unless you can solve the riddle written in our blood on the wall.' The temperature drops to freezing, and you see your breath as the spirits of the dead family surround you.",
            question: "Written in blood on the wall: 'We died on the 13th day. Daddy's age was 42. Mommy was 38. Sarah was 7, Tommy was 9, and baby Emma was 3. Add our ages together, then subtract the day we died. The answer is the code to escape our eternal torment.'",
            answer: "86",
            hint: "Add all the family members' ages, then subtract 13..."
        },
        graveyard: {
            title: "⚰️ RESURRECTION CEMETERY - The Undead Rise",
            story: "You're trapped in Resurrection Cemetery at the stroke of midnight during a blood moon. The ground begins to crack and split open as rotting hands burst through the soil. Dozens of decomposing corpses claw their way out of their graves, their empty eye sockets glowing with unholy fire. The zombie horde surrounds you, moaning and reaching for your flesh. Suddenly, the cemetery caretaker's ghost appears - he was buried alive here 50 years ago. His jaw hangs broken, and maggots crawl from his mouth as he speaks: 'These hungry dead will feast on your organs unless you solve the riddle carved into my tombstone. Get it wrong, and they'll bury you alive like they did to me, and you'll feel every shovel of dirt as it slowly suffocates you.'",
            question: "The ghost points to his cracked tombstone: 'I was buried in 1973. I lived for 47 years. The zombies that rose tonight number 23. Multiply my age by the year I died, then divide by the number of zombies. Round down to the nearest whole number. This is your salvation.'",
            answer: "4",
            hint: "47 × 1973 ÷ 23 = ? (round down)"
        },
        hospital: {
            title: "🏥 ABANDONED HOSPITAL - Medical Nightmare",
            story: "You're trapped in the psychiatric ward of an abandoned hospital. The patient files are scattered everywhere. One file contains the code to unlock the exit, but it's written in medical terminology.",
            question: "Patient #666's file shows: 'Temperature: 98.6°F, Pulse: 72 BPM, Blood Pressure: 120/80'. The exit code is the sum of all these numbers. What is it?",
            answer: "370.6",
            hint: "Add: 98.6 + 72 + 120 + 80 = ?"
        },
        forest: {
            title: "🌲 DARK FOREST - The Witch's Riddle",
            story: "You're lost in a cursed forest where a witch has trapped you. She offers you freedom if you can solve her riddle about the ingredients for her most powerful spell.",
            question: "The witch cackles: 'I need 3 bat wings, 2 spider eyes, 5 toad tongues, and 1 dragon scale. But I already have half of each ingredient. How many total ingredients do I still need to collect?'",
            answer: "5.5",
            hint: "Calculate half of each ingredient, then add them up..."
        },
        mirror: {
            title: "🪞 CURSED MIRROR - Reflection of Evil",
            story: "You've found an ancient cursed mirror that shows not your reflection, but your darkest fears. To break the curse, you must solve the mirror's riddle about reflections and reality.",
            question: "The mirror shows you holding 4 candles, but in reality you hold 7. Your reflection shows 3 books, but you see 6. The pattern continues: reflection shows 2 keys, reality has how many?",
            answer: "5",
            hint: "Look at the pattern: reality is always 3 more than the reflection..."
        },
        basement: {
            title: "🔗 TORTURE CHAMBER - Escape the Dungeon",
            story: "You're chained in a medieval torture chamber. The only way out is to solve the executioner's puzzle. Three keys hang on the wall, but only one will free you. Choose wrong, and face eternal torment.",
            question: "Three prisoners were here before you. The first stayed 3 days, the second stayed 7 days, the third stayed 15 days. Following this pattern, how many days would the fourth prisoner stay?",
            answer: "31",
            hint: "Look at the pattern: 3, 7, 15... each number is double the previous plus 1..."
        },
        ritual: {
            title: "👹 DEMONIC RITUAL - Break the Curse",
            story: "You've stumbled into a demonic ritual circle. The only way to break the curse is to arrange the ritual symbols in the correct order. The demons whisper clues in ancient tongues.",
            question: "The ritual requires 5 symbols in order: 🔥💀⭐🌙🗡️. The fire comes before the skull, the star comes after the moon, the sword is last, and the skull comes before the moon. What's the correct order?",
            answer: "🔥💀🌙⭐🗡️",
            hint: "Work through the clues step by step: fire first, then skull, then moon, then star, sword last..."
        },
        zombie: {
            title: "🧟 ZOMBIE APOCALYPSE - Survival Strategy",
            story: "The zombie apocalypse has begun! You're barricaded in a building with limited supplies. You must calculate your survival time based on your resources and the zombie threat level.",
            question: "You have 20 cans of food (each lasts 2 days), 15 bottles of water (each lasts 1 day), and 10 medical kits. Zombies break through every 5 days, using 1 medical kit each time. How many days can you survive?",
            answer: "15",
            hint: "Food lasts 40 days, water lasts 15 days, medical kits last 50 days. The limiting factor is..."
        },
        final: {
            title: "👑 FINAL NIGHTMARE - The Ultimate Test",
            story: "You've reached the final challenge. The Nightmare King himself appears before you. This is the ultimate test that combines elements from all your previous trials. Only the truly brave can solve this.",
            question: "The Nightmare King presents you with a final riddle: 'I am the sum of all your fears. Take the number of puzzles you've completed (including this one), multiply by the number of letters in NIGHTMARE, subtract the number of deadly sins, and add the number of horsemen of the apocalypse. What am I?'",
            answer: "94",
            hint: "10 puzzles × 9 letters in NIGHTMARE - 7 sins + 4 horsemen = ?"
        }
    };

    const puzzle = puzzles[puzzleType];
    if (!puzzle) return '<p>Puzzle not found...</p>';

    return `
        <h2>${puzzle.title}</h2>
        <div class="puzzle-story">
            <p><strong>Your Nightmare:</strong></p>
            <p>${puzzle.story}</p>
        </div>
        <div class="puzzle-question">
            <h3>The Challenge:</h3>
            <p>${puzzle.question}</p>
        </div>
        <div class="puzzle-input-section">
            <input type="text" id="puzzleAnswer" class="puzzle-input" placeholder="Enter your answer..." autocomplete="off">
            <br>
            <button onclick="checkAnswer('${puzzle.answer}')" class="submit-btn">Submit Answer</button>
            <button onclick="showHint('${puzzle.hint}')" class="hint-btn">Get Hint (if you dare...)</button>
        </div>
        <div id="puzzleResult"></div>
        <div id="puzzleHint"></div>
    `;
}

// Check puzzle answer
function checkAnswer(correctAnswer) {
    const userAnswer = document.getElementById('puzzleAnswer').value.trim();
    const resultDiv = document.getElementById('puzzleResult');

    if (userAnswer.toLowerCase() === correctAnswer.toLowerCase()) {
        // Correct answer
        completedPuzzles.add(currentPuzzle);
        updateProgress();

        resultDiv.innerHTML = `
            <div class="success-message">
                <h3>🎉 CONGRATULATIONS! 🎉</h3>
                <p>You have survived this nightmare!</p>
                <p>The spirits are pleased with your wisdom...</p>
            </div>
        `;

        playSuccessSound();

        // Check if all puzzles completed
        if (completedPuzzles.size === 10) {
            setTimeout(() => {
                showVictoryMessage();
            }, 2000);
        } else {
            setTimeout(() => {
                closePuzzle();
            }, 3000);
        }

    } else {
        // Wrong answer
        resultDiv.innerHTML = `
            <div class="error-message">
                <h3>💀 WRONG! 💀</h3>
                <p>The darkness grows stronger...</p>
                <p>Try again, if you dare!</p>
            </div>
        `;

        playScream();
        shakeScreen();

        // Clear the input
        document.getElementById('puzzleAnswer').value = '';
    }
}

// Show hint
function showHint(hint) {
    const hintDiv = document.getElementById('puzzleHint');
    hintDiv.innerHTML = `
        <div style="background: linear-gradient(135deg, #333300, #666600); border: 2px solid #ffff00; border-radius: 10px; padding: 15px; margin: 15px 0;">
            <h4>👻 Hint from the Spirits:</h4>
            <p>${hint}</p>
        </div>
    `;

    playHorrorSound();
}

// Update progress
function updateProgress() {
    const progressFill = document.getElementById('progressFill');
    const completedCount = document.getElementById('completedCount');

    const percentage = (completedPuzzles.size / 10) * 100;
    progressFill.style.width = percentage + '%';
    completedCount.textContent = completedPuzzles.size;

    // Mark completed puzzles
    document.querySelectorAll('.puzzle-card').forEach(card => {
        const puzzleType = card.getAttribute('data-puzzle');
        if (completedPuzzles.has(puzzleType)) {
            card.style.opacity = '0.6';
            card.style.border = '3px solid #00ff00';
            card.innerHTML += '<div style="position: absolute; top: 10px; right: 10px; font-size: 2rem;">✅</div>';
        }
    });
}

// Show victory message
function showVictoryMessage() {
    const modal = document.getElementById('puzzleModal');
    const content = document.getElementById('puzzleContent');

    content.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <h2 style="font-size: 3rem; color: #ffff00; text-shadow: 0 0 20px #ffff00;">
                🏆 ULTIMATE VICTORY! 🏆
            </h2>
            <p style="font-size: 1.5rem; margin: 20px 0; color: #ffcccc;">
                You have conquered all 10 nightmares!
            </p>
            <p style="font-size: 1.2rem; margin: 15px 0;">
                The Nightmare Realm bows before your courage and wisdom.
            </p>
            <p style="font-size: 1.2rem; margin: 15px 0;">
                You are now the Master of Nightmares!
            </p>
            <div style="margin: 30px 0; font-size: 3rem;">
                👑💀🔥💀👑
            </div>
            <button onclick="resetGame()" class="submit-btn" style="font-size: 1.5rem; padding: 20px 40px;">
                Face the Nightmares Again
            </button>
        </div>
    `;

    modal.style.display = 'block';

    // Victory effects
    document.body.style.background = 'linear-gradient(45deg, #ffff00, #ff6600, #ff0000, #ffff00)';
    document.body.style.backgroundSize = '400% 400%';
    document.body.style.animation = 'bloodBackground 2s ease infinite';
}

// Reset game
function resetGame() {
    completedPuzzles.clear();
    updateProgress();
    closePuzzle();

    // Reset puzzle cards
    document.querySelectorAll('.puzzle-card').forEach(card => {
        card.style.opacity = '1';
        card.style.border = '2px solid #ff0000';
        const checkmark = card.querySelector('div[style*="position: absolute"]');
        if (checkmark) {
            checkmark.remove();
        }
    });

    // Reset background
    document.body.style.background = 'linear-gradient(45deg, #000000, #1a0000, #330000, #000000)';
    document.body.style.backgroundSize = '400% 400%';
    document.body.style.animation = 'bloodBackground 10s ease infinite';

    showHorrorMessage("The nightmares have reset... Face them again if you dare!");
}

// Horror effects
function playScream() {
    if (horrorSounds.scream) {
        horrorSounds.scream.currentTime = 0;
        horrorSounds.scream.volume = 0.5;
        horrorSounds.scream.play().catch(e => console.log('Audio blocked'));
    }
}

function playHorrorSound() {
    // Create a random horror sound effect
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(100, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(50, audioContext.currentTime + 0.5);

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.5);
}

function playSuccessSound() {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.3);

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.3);
}

function shakeScreen() {
    document.body.style.animation = 'shake 0.5s';
    setTimeout(() => {
        document.body.style.animation = '';
    }, 500);
}

function screenFlash() {
    const flash = document.createElement('div');
    flash.style.position = 'fixed';
    flash.style.top = '0';
    flash.style.left = '0';
    flash.style.width = '100%';
    flash.style.height = '100%';
    flash.style.background = '#ffffff';
    flash.style.opacity = '0.8';
    flash.style.zIndex = '9999';
    flash.style.pointerEvents = 'none';

    document.body.appendChild(flash);

    setTimeout(() => {
        flash.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(flash);
        }, 100);
    }, 50);
}

function changeCursor() {
    const cursors = [
        'url("data:image/svg+xml;utf8,<svg xmlns=\\"http://www.w3.org/2000/svg\\" width=\\"20\\" height=\\"20\\" viewBox=\\"0 0 20 20\\"><text y=\\"16\\" font-size=\\"16\\">💀</text></svg>"), auto',
        'url("data:image/svg+xml;utf8,<svg xmlns=\\"http://www.w3.org/2000/svg\\" width=\\"20\\" height=\\"20\\" viewBox=\\"0 0 20 20\\"><text y=\\"16\\" font-size=\\"16\\">🔥</text></svg>"), auto',
        'url("data:image/svg+xml;utf8,<svg xmlns=\\"http://www.w3.org/2000/svg\\" width=\\"20\\" height=\\"20\\" viewBox=\\"0 0 20 20\\"><text y=\\"16\\" font-size=\\"16\\">👻</text></svg>"), auto',
        'url("data:image/svg+xml;utf8,<svg xmlns=\\"http://www.w3.org/2000/svg\\" width=\\"20\\" height=\\"20\\" viewBox=\\"0 0 20 20\\"><text y=\\"16\\" font-size=\\"16\\">🗡️</text></svg>"), auto'
    ];

    document.body.style.cursor = cursors[Math.floor(Math.random() * cursors.length)];

    setTimeout(() => {
        document.body.style.cursor = cursors[0]; // Back to skull
    }, 3000);
}

function randomHorrorEffect() {
    const effects = [screenFlash, playHorrorSound, changeCursor];
    const randomEffect = effects[Math.floor(Math.random() * effects.length)];
    randomEffect();
}

function showHorrorMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.style.position = 'fixed';
    messageDiv.style.top = '50%';
    messageDiv.style.left = '50%';
    messageDiv.style.transform = 'translate(-50%, -50%)';
    messageDiv.style.background = 'linear-gradient(135deg, #1a0000, #330000)';
    messageDiv.style.border = '3px solid #ff0000';
    messageDiv.style.borderRadius = '15px';
    messageDiv.style.padding = '30px';
    messageDiv.style.color = '#ffcccc';
    messageDiv.style.fontSize = '1.5rem';
    messageDiv.style.textAlign = 'center';
    messageDiv.style.zIndex = '10000';
    messageDiv.style.boxShadow = '0 0 30px #ff0000';
    messageDiv.style.fontFamily = 'Butcherman, cursive';
    messageDiv.innerHTML = message;

    document.body.appendChild(messageDiv);

    setTimeout(() => {
        messageDiv.style.opacity = '0';
        messageDiv.style.transition = 'opacity 0.5s';
        setTimeout(() => {
            document.body.removeChild(messageDiv);
        }, 500);
    }, 3000);
}

// Add CSS for shake animation
const shakeCSS = `
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
    20%, 40%, 60%, 80% { transform: translateX(10px); }
}
`;

const style = document.createElement('style');
style.textContent = shakeCSS;
document.head.appendChild(style);

// Console message for brave developers
console.log(`
🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥
💀 WELCOME TO THE NIGHTMARE REALM 💀
🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥

You've opened the developer console...
Are you trying to cheat? 😈

The nightmares are watching you...
They know your every move...

Good luck, brave soul.
You'll need it.

🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥
`);