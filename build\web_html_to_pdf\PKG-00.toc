('C:\\MyCode\\build\\web_html_to_pdf\\web_html_to_pdf.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'C:\\MyCode\\build\\web_html_to_pdf\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'C:\\MyCode\\build\\web_html_to_pdf\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\MyCode\\build\\web_html_to_pdf\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\MyCode\\build\\web_html_to_pdf\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\MyCode\\build\\web_html_to_pdf\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\MyCode\\build\\web_html_to_pdf\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\MyCode\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\MyCode\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\MyCode\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\MyCode\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\MyCode\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'C:\\MyCode\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'C:\\MyCode\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\MyCode\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('web_html_to_pdf', 'C:\\MyCode\\web_html_to_pdf.py', 'PYSOURCE'),
  ('playwright\\driver\\node.exe',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\node.exe',
   'BINARY'),
  ('playwright\\driver\\package\\bin\\PrintDeps.exe',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\PrintDeps.exe',
   'BINARY'),
  ('python310.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python310.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes310.dll',
   'C:\\MyCode\\.venv\\lib\\site-packages\\pywin32_system32\\pywintypes310.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom310.dll',
   'C:\\MyCode\\.venv\\lib\\site-packages\\pywin32_system32\\pythoncom310.dll',
   'BINARY'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\select.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\markupsafe\\_speedups.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32com\\shell\\shell.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_msi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_msi.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PIL\\_webp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PIL\\_imagingtk.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PIL\\_imagingcms.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PIL\\_imagingmath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\charset_normalizer\\md.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy\\random\\_philox.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy\\random\\_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy\\random\\_common.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\yaml\\_yaml.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PIL\\_imaging.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\greenlet\\_greenlet.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp310-win_amd64.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\_cffi_backend.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'C:\\MyCode\\.venv\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python3.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('PyPDF2\\__init__.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\__init__.py',
   'DATA'),
  ('PyPDF2\\_cmap.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\_cmap.py',
   'DATA'),
  ('PyPDF2\\_codecs\\__init__.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\_codecs\\__init__.py',
   'DATA'),
  ('PyPDF2\\_codecs\\adobe_glyphs.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\_codecs\\adobe_glyphs.py',
   'DATA'),
  ('PyPDF2\\_codecs\\pdfdoc.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\_codecs\\pdfdoc.py',
   'DATA'),
  ('PyPDF2\\_codecs\\std.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\_codecs\\std.py',
   'DATA'),
  ('PyPDF2\\_codecs\\symbol.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\_codecs\\symbol.py',
   'DATA'),
  ('PyPDF2\\_codecs\\zapfding.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\_codecs\\zapfding.py',
   'DATA'),
  ('PyPDF2\\_encryption.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\_encryption.py',
   'DATA'),
  ('PyPDF2\\_merger.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\_merger.py',
   'DATA'),
  ('PyPDF2\\_page.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\_page.py',
   'DATA'),
  ('PyPDF2\\_protocols.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\_protocols.py',
   'DATA'),
  ('PyPDF2\\_reader.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\_reader.py',
   'DATA'),
  ('PyPDF2\\_security.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\_security.py',
   'DATA'),
  ('PyPDF2\\_utils.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\_utils.py',
   'DATA'),
  ('PyPDF2\\_version.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\_version.py',
   'DATA'),
  ('PyPDF2\\_writer.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\_writer.py',
   'DATA'),
  ('PyPDF2\\constants.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\constants.py',
   'DATA'),
  ('PyPDF2\\errors.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\errors.py',
   'DATA'),
  ('PyPDF2\\filters.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\filters.py',
   'DATA'),
  ('PyPDF2\\generic\\__init__.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\generic\\__init__.py',
   'DATA'),
  ('PyPDF2\\generic\\_annotations.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\generic\\_annotations.py',
   'DATA'),
  ('PyPDF2\\generic\\_base.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\generic\\_base.py',
   'DATA'),
  ('PyPDF2\\generic\\_data_structures.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\generic\\_data_structures.py',
   'DATA'),
  ('PyPDF2\\generic\\_fit.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\generic\\_fit.py',
   'DATA'),
  ('PyPDF2\\generic\\_outline.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\generic\\_outline.py',
   'DATA'),
  ('PyPDF2\\generic\\_rectangle.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\generic\\_rectangle.py',
   'DATA'),
  ('PyPDF2\\generic\\_utils.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\generic\\_utils.py',
   'DATA'),
  ('PyPDF2\\pagerange.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\pagerange.py',
   'DATA'),
  ('PyPDF2\\papersizes.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\papersizes.py',
   'DATA'),
  ('PyPDF2\\py.typed',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\py.typed',
   'DATA'),
  ('PyPDF2\\types.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\types.py',
   'DATA'),
  ('PyPDF2\\xmp.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\PyPDF2\\xmp.py',
   'DATA'),
  ('playwright-1.38.0.dist-info\\INSTALLER',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright-1.38.0.dist-info\\INSTALLER',
   'DATA'),
  ('playwright-1.38.0.dist-info\\LICENSE',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright-1.38.0.dist-info\\LICENSE',
   'DATA'),
  ('playwright-1.38.0.dist-info\\METADATA',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright-1.38.0.dist-info\\METADATA',
   'DATA'),
  ('playwright-1.38.0.dist-info\\RECORD',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright-1.38.0.dist-info\\RECORD',
   'DATA'),
  ('playwright-1.38.0.dist-info\\REQUESTED',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright-1.38.0.dist-info\\REQUESTED',
   'DATA'),
  ('playwright-1.38.0.dist-info\\WHEEL',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright-1.38.0.dist-info\\WHEEL',
   'DATA'),
  ('playwright-1.38.0.dist-info\\entry_points.txt',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright-1.38.0.dist-info\\entry_points.txt',
   'DATA'),
  ('playwright-1.38.0.dist-info\\top_level.txt',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright-1.38.0.dist-info\\top_level.txt',
   'DATA'),
  ('playwright\\__init__.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\__init__.py',
   'DATA'),
  ('playwright\\__main__.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\__main__.py',
   'DATA'),
  ('playwright\\_impl\\__init__.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\__init__.py',
   'DATA'),
  ('playwright\\_impl\\__pyinstaller\\__init__.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\__pyinstaller\\__init__.py',
   'DATA'),
  ('playwright\\_impl\\__pyinstaller\\hook-playwright.async_api.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\__pyinstaller\\hook-playwright.async_api.py',
   'DATA'),
  ('playwright\\_impl\\__pyinstaller\\hook-playwright.sync_api.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\__pyinstaller\\hook-playwright.sync_api.py',
   'DATA'),
  ('playwright\\_impl\\_accessibility.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_accessibility.py',
   'DATA'),
  ('playwright\\_impl\\_api_structures.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_api_structures.py',
   'DATA'),
  ('playwright\\_impl\\_api_types.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_api_types.py',
   'DATA'),
  ('playwright\\_impl\\_artifact.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_artifact.py',
   'DATA'),
  ('playwright\\_impl\\_assertions.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_assertions.py',
   'DATA'),
  ('playwright\\_impl\\_async_base.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_async_base.py',
   'DATA'),
  ('playwright\\_impl\\_browser.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_browser.py',
   'DATA'),
  ('playwright\\_impl\\_browser_context.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_browser_context.py',
   'DATA'),
  ('playwright\\_impl\\_browser_type.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_browser_type.py',
   'DATA'),
  ('playwright\\_impl\\_cdp_session.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_cdp_session.py',
   'DATA'),
  ('playwright\\_impl\\_connection.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_connection.py',
   'DATA'),
  ('playwright\\_impl\\_console_message.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_console_message.py',
   'DATA'),
  ('playwright\\_impl\\_dialog.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_dialog.py',
   'DATA'),
  ('playwright\\_impl\\_download.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_download.py',
   'DATA'),
  ('playwright\\_impl\\_driver.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_driver.py',
   'DATA'),
  ('playwright\\_impl\\_element_handle.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_element_handle.py',
   'DATA'),
  ('playwright\\_impl\\_event_context_manager.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_event_context_manager.py',
   'DATA'),
  ('playwright\\_impl\\_fetch.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_fetch.py',
   'DATA'),
  ('playwright\\_impl\\_file_chooser.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_file_chooser.py',
   'DATA'),
  ('playwright\\_impl\\_frame.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_frame.py',
   'DATA'),
  ('playwright\\_impl\\_har_router.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_har_router.py',
   'DATA'),
  ('playwright\\_impl\\_helper.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_helper.py',
   'DATA'),
  ('playwright\\_impl\\_impl_to_api_mapping.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_impl_to_api_mapping.py',
   'DATA'),
  ('playwright\\_impl\\_input.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_input.py',
   'DATA'),
  ('playwright\\_impl\\_js_handle.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_js_handle.py',
   'DATA'),
  ('playwright\\_impl\\_json_pipe.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_json_pipe.py',
   'DATA'),
  ('playwright\\_impl\\_local_utils.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_local_utils.py',
   'DATA'),
  ('playwright\\_impl\\_locator.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_locator.py',
   'DATA'),
  ('playwright\\_impl\\_map.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_map.py',
   'DATA'),
  ('playwright\\_impl\\_network.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_network.py',
   'DATA'),
  ('playwright\\_impl\\_object_factory.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_object_factory.py',
   'DATA'),
  ('playwright\\_impl\\_page.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_page.py',
   'DATA'),
  ('playwright\\_impl\\_path_utils.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_path_utils.py',
   'DATA'),
  ('playwright\\_impl\\_playwright.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_playwright.py',
   'DATA'),
  ('playwright\\_impl\\_selectors.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_selectors.py',
   'DATA'),
  ('playwright\\_impl\\_set_input_files_helpers.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_set_input_files_helpers.py',
   'DATA'),
  ('playwright\\_impl\\_str_utils.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_str_utils.py',
   'DATA'),
  ('playwright\\_impl\\_stream.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_stream.py',
   'DATA'),
  ('playwright\\_impl\\_sync_base.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_sync_base.py',
   'DATA'),
  ('playwright\\_impl\\_tracing.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_tracing.py',
   'DATA'),
  ('playwright\\_impl\\_transport.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_transport.py',
   'DATA'),
  ('playwright\\_impl\\_video.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_video.py',
   'DATA'),
  ('playwright\\_impl\\_wait_helper.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_wait_helper.py',
   'DATA'),
  ('playwright\\_impl\\_web_error.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_web_error.py',
   'DATA'),
  ('playwright\\_impl\\_writable_stream.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_impl\\_writable_stream.py',
   'DATA'),
  ('playwright\\_repo_version.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\_repo_version.py',
   'DATA'),
  ('playwright\\async_api\\__init__.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\async_api\\__init__.py',
   'DATA'),
  ('playwright\\async_api\\_context_manager.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\async_api\\_context_manager.py',
   'DATA'),
  ('playwright\\async_api\\_generated.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\async_api\\_generated.py',
   'DATA'),
  ('playwright\\driver\\LICENSE',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\LICENSE',
   'DATA'),
  ('playwright\\driver\\README.md',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\README.md',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\ThirdPartyNotices.txt',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\ThirdPartyNotices.txt',
   'DATA'),
  ('playwright\\driver\\package\\api.json',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\api.json',
   'DATA'),
  ('playwright\\driver\\package\\bin\\README.md',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\bin\\install_media_pack.ps1',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\install_media_pack.ps1',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_linux.sh',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_mac.sh',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_win.ps1',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_linux.sh',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_mac.sh',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_win.ps1',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_linux.sh',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_mac.sh',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_win.ps1',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_linux.sh',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_mac.sh',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_win.ps1',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_linux.sh',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_mac.sh',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_win.ps1',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\browsers.json',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\browsers.json',
   'DATA'),
  ('playwright\\driver\\package\\cli.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\cli.js',
   'DATA'),
  ('playwright\\driver\\package\\index.d.ts',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\index.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\index.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\index.mjs',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\index.mjs',
   'DATA'),
  ('playwright\\driver\\package\\lib\\androidServerImpl.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\androidServerImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\browserServerImpl.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\browserServerImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\cli.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\cli.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\driver.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\driver.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\program.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\program.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\accessibility.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\accessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\android.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\android.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\api.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\api.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\artifact.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\artifact.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browser.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browserContext.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browserContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browserType.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browserType.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\cdpSession.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\cdpSession.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\channelOwner.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\channelOwner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clientHelper.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clientHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clientInstrumentation.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clientInstrumentation.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\connection.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\connection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\consoleMessage.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\consoleMessage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\coverage.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\coverage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\dialog.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\dialog.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\download.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\download.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\electron.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\electron.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\elementHandle.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\elementHandle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\events.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\events.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\fetch.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\fetch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\fileChooser.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\fileChooser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\frame.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\frame.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\harRouter.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\harRouter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\input.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\input.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\jsHandle.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\jsHandle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\jsonPipe.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\jsonPipe.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\localUtils.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\localUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\locator.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\locator.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\network.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\page.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\page.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\playwright.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\playwright.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\selectors.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\selectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\stream.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\stream.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\tracing.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\tracing.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\types.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\video.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\video.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\waiter.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\waiter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\webError.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\webError.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\worker.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\worker.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\writableStream.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\writableStream.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\debugLogger.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\common\\debugLogger.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\errors.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\common\\errors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\socksProxy.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\common\\socksProxy.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\timeoutSettings.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\common\\timeoutSettings.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\types.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\common\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\consoleApiSource.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\consoleApiSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\injectedScriptSource.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\injectedScriptSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\recorderSource.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\recorderSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\utilityScriptSource.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\utilityScriptSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\colorUtils.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\colorUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\compare.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\compare.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\imageChannel.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\imageChannel.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\stats.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\stats.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\inProcessFactory.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\inProcessFactory.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\inprocess.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\inprocess.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\outofprocess.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\outofprocess.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\debug.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\debug.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\serializers.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\serializers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\transport.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\transport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\validator.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\validator.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\validatorPrimitives.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\validatorPrimitives.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\remote\\playwrightConnection.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\remote\\playwrightConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\remote\\playwrightServer.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\remote\\playwrightServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\accessibility.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\accessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\android\\android.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\android\\android.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\android\\backendAdb.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\android\\backendAdb.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\artifact.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\artifact.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browser.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browserContext.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browserContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browserType.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browserType.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\appIcon.png',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\appIcon.png',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\chromium.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\chromium.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\chromiumSwitches.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\chromiumSwitches.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crAccessibility.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crBrowser.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crConnection.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crCoverage.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crCoverage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crDevTools.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crDevTools.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crDragDrop.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crDragDrop.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crExecutionContext.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crInput.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crNetworkManager.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crPage.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crPdf.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crPdf.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crProtocolHelper.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crProtocolHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crServiceWorker.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crServiceWorker.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\defaultFontFamilies.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\defaultFontFamilies.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\videoRecorder.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\videoRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\console.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\console.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\cookieStore.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\cookieStore.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\debugController.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\debugController.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\debugger.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\debugger.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\deviceDescriptors.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\deviceDescriptors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\deviceDescriptorsSource.json',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\deviceDescriptorsSource.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dialog.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dialog.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\androidDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\androidDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\artifactDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\artifactDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserContextDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserContextDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserTypeDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserTypeDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\cdpSessionDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\cdpSessionDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\consoleMessageDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\consoleMessageDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\debugControllerDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\debugControllerDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\dialogDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\dialogDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\dispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\dispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\electronDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\electronDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\elementHandlerDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\elementHandlerDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\frameDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\frameDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\jsHandleDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\jsHandleDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\jsonPipeDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\jsonPipeDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\localUtilsDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\localUtilsDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\networkDispatchers.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\networkDispatchers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\pageDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\pageDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\playwrightDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\playwrightDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\selectorsDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\selectorsDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\streamDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\streamDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\tracingDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\tracingDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\writableStreamDispatcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\writableStreamDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dom.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dom.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\download.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\download.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\electron\\electron.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\electron\\electron.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\electron\\loader.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\electron\\loader.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fetch.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fetch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fileChooser.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fileChooser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffAccessibility.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffBrowser.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffConnection.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffExecutionContext.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffInput.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffNetworkManager.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffPage.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\firefox.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\firefox.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\formData.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\formData.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\frameSelectors.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\frameSelectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\frames.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\frames.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\har\\harRecorder.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\har\\harRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\har\\harTracer.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\har\\harTracer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\helper.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\helper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\index.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\input.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\input.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\instrumentation.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\instrumentation.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\isomorphic\\utilityScriptSerializers.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\isomorphic\\utilityScriptSerializers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\javascript.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\javascript.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\launchApp.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\launchApp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\macEditingCommands.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\macEditingCommands.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\network.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\page.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\page.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\pipeTransport.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\pipeTransport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\playwright.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\playwright.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\progress.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\progress.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\protocolError.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\protocolError.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\codeGenerator.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\codeGenerator.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\csharp.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\csharp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\java.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\java.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\javascript.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\javascript.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\jsonl.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\jsonl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\language.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\language.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\python.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\python.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderActions.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderActions.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderApp.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderApp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderUtils.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\utils.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\utils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\browserFetcher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\browserFetcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\dependencies.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\dependencies.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\index.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\nativeDeps.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\nativeDeps.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\oopDownloadBrowserMain.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\oopDownloadBrowserMain.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\screenshotter.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\screenshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\selectors.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\selectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\socksInterceptor.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\socksInterceptor.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotter.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotterInjected.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotterInjected.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\tracing.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\tracing.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\test\\inMemorySnapshotter.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\test\\inMemorySnapshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\viewer\\traceViewer.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\viewer\\traceViewer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\transport.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\transport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\types.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\usKeyboardLayout.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\usKeyboardLayout.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\webkit.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\webkit.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkAccessibility.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkBrowser.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkConnection.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkExecutionContext.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkInput.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkInterceptableRequest.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkInterceptableRequest.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkPage.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkProvisionalPage.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkProvisionalPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkWorkers.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkWorkers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\third_party\\diff_match_patch.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\third_party\\diff_match_patch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\third_party\\pixelmatch.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\third_party\\pixelmatch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundle.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundleImpl\\index.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundleImpl\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundleImpl\\xdg-open',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundleImpl\\xdg-open',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\ascii.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\ascii.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\comparators.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\comparators.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\crypto.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\crypto.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\debug.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\debug.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\env.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\env.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\eventsHelper.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\eventsHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\fileUtils.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\fileUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\glob.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\glob.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\happy-eyeballs.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\happy-eyeballs.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\headers.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\headers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\hostPlatform.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\hostPlatform.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\httpServer.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\httpServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\index.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\cssParser.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\cssParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\cssTokenizer.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\cssTokenizer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorGenerators.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorGenerators.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorParser.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorUtils.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\selectorParser.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\selectorParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\stringUtils.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\stringUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\traceUtils.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\traceUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\linuxUtils.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\linuxUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\manualPromise.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\manualPromise.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\mimeType.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\mimeType.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\multimap.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\multimap.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\network.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\processLauncher.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\processLauncher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\profiler.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\profiler.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\rtti.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\rtti.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\spawnAsync.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\spawnAsync.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\stackTrace.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\stackTrace.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\task.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\task.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\time.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\time.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\timeoutRunner.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\timeoutRunner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\traceUtils.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\traceUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\userAgent.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\userAgent.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\zipFile.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\zipFile.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\zones.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\zones.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\htmlReport\\index.html',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\htmlReport\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-5d0f417c.css',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-5d0f417c.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-618d7c84.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-618d7c84.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codicon-79f233d0.ttf',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codicon-79f233d0.ttf',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-5e8a1003.css',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-5e8a1003.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-9f2b786b.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-9f2b786b.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\index.html',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\playwright-logo.svg',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\playwright-logo.svg',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\codeMirrorModule-344d0291.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\codeMirrorModule-344d0291.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\wsPort-2e1dc307.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\wsPort-2e1dc307.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\xtermModule-443332e6.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\xtermModule-443332e6.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\codeMirrorModule.5d0f417c.css',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\codeMirrorModule.5d0f417c.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\codicon.79f233d0.ttf',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\codicon.79f233d0.ttf',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.26eb91d4.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.26eb91d4.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.a60f70ab.css',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.a60f70ab.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.html',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\playwright-logo.svg',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\playwright-logo.svg',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\snapshot.html',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\snapshot.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\sw.bundle.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\sw.bundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.0f8b778a.css',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.0f8b778a.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.79197e16.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.79197e16.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.html',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\wsPort.42ee6414.css',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\wsPort.42ee6414.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\xtermModule.6428296b.css',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\xtermModule.6428296b.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\zipBundle.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\zipBundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\zipBundleImpl.js',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\lib\\zipBundleImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\package.json',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\package.json',
   'DATA'),
  ('playwright\\driver\\package\\protocol.yml',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\protocol.yml',
   'DATA'),
  ('playwright\\driver\\package\\types\\protocol.d.ts',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\types\\protocol.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\types\\structs.d.ts',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\types\\structs.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\types\\types.d.ts',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\package\\types\\types.d.ts',
   'DATA'),
  ('playwright\\driver\\playwright.cmd',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\driver\\playwright.cmd',
   'DATA'),
  ('playwright\\py.typed',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\py.typed',
   'DATA'),
  ('playwright\\sync_api\\__init__.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\sync_api\\__init__.py',
   'DATA'),
  ('playwright\\sync_api\\_context_manager.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\sync_api\\_context_manager.py',
   'DATA'),
  ('playwright\\sync_api\\_generated.py',
   'C:\\MyCode\\.venv\\lib\\site-packages\\playwright\\sync_api\\_generated.py',
   'DATA'),
  ('templates\\index.html', 'C:\\MyCode\\templates\\index.html', 'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\METADATA',
   'C:\\MyCode\\.venv\\lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\WHEEL',
   'C:\\MyCode\\.venv\\lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\INSTALLER',
   'C:\\MyCode\\.venv\\lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\licenses\\LICENSE',
   'C:\\MyCode\\.venv\\lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\top_level.txt',
   'C:\\MyCode\\.venv\\lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\RECORD',
   'C:\\MyCode\\.venv\\lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\RECORD',
   'C:\\MyCode\\.venv\\lib\\site-packages\\cryptography-45.0.2.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\METADATA',
   'C:\\MyCode\\.venv\\lib\\site-packages\\cryptography-45.0.2.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\licenses\\LICENSE.BSD',
   'C:\\MyCode\\.venv\\lib\\site-packages\\cryptography-45.0.2.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\REQUESTED',
   'C:\\MyCode\\.venv\\lib\\site-packages\\cryptography-45.0.2.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\WHEEL',
   'C:\\MyCode\\.venv\\lib\\site-packages\\cryptography-45.0.2.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\INSTALLER',
   'C:\\MyCode\\.venv\\lib\\site-packages\\cryptography-45.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\licenses\\LICENSE.APACHE',
   'C:\\MyCode\\.venv\\lib\\site-packages\\cryptography-45.0.2.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\licenses\\LICENSE',
   'C:\\MyCode\\.venv\\lib\\site-packages\\cryptography-45.0.2.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('setuptools-63.2.0.dist-info\\entry_points.txt',
   'C:\\MyCode\\.venv\\lib\\site-packages\\setuptools-63.2.0.dist-info\\entry_points.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'C:\\MyCode\\.venv\\lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('setuptools-63.2.0.dist-info\\top_level.txt',
   'C:\\MyCode\\.venv\\lib\\site-packages\\setuptools-63.2.0.dist-info\\top_level.txt',
   'DATA'),
  ('numpy-2.2.5.dist-info\\INSTALLER',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy-2.2.5.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.0.dist-info\\licenses\\LICENSE.txt',
   'C:\\MyCode\\.venv\\lib\\site-packages\\click-8.2.0.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'C:\\MyCode\\.venv\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools-63.2.0.dist-info\\REQUESTED',
   'C:\\MyCode\\.venv\\lib\\site-packages\\setuptools-63.2.0.dist-info\\REQUESTED',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'C:\\MyCode\\.venv\\lib\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('flask-3.1.1.dist-info\\INSTALLER',
   'C:\\MyCode\\.venv\\lib\\site-packages\\flask-3.1.1.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.1.dist-info\\REQUESTED',
   'C:\\MyCode\\.venv\\lib\\site-packages\\flask-3.1.1.dist-info\\REQUESTED',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'C:\\MyCode\\.venv\\lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'C:\\MyCode\\.venv\\lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\MyCode\\.venv\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('flask-3.1.1.dist-info\\entry_points.txt',
   'C:\\MyCode\\.venv\\lib\\site-packages\\flask-3.1.1.dist-info\\entry_points.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\MyCode\\.venv\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('click-8.2.0.dist-info\\METADATA',
   'C:\\MyCode\\.venv\\lib\\site-packages\\click-8.2.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools-63.2.0.dist-info\\WHEEL',
   'C:\\MyCode\\.venv\\lib\\site-packages\\setuptools-63.2.0.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.5.dist-info\\DELVEWHEEL',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy-2.2.5.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.5.dist-info\\entry_points.txt',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy-2.2.5.dist-info\\entry_points.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'C:\\MyCode\\.venv\\lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'C:\\MyCode\\.venv\\lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\MyCode\\.venv\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\MyCode\\.venv\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\MyCode\\.venv\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.1.dist-info\\WHEEL',
   'C:\\MyCode\\.venv\\lib\\site-packages\\flask-3.1.1.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-63.2.0.dist-info\\LICENSE',
   'C:\\MyCode\\.venv\\lib\\site-packages\\setuptools-63.2.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools-63.2.0.dist-info\\RECORD',
   'C:\\MyCode\\.venv\\lib\\site-packages\\setuptools-63.2.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\MyCode\\.venv\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('flask-3.1.1.dist-info\\METADATA',
   'C:\\MyCode\\.venv\\lib\\site-packages\\flask-3.1.1.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.0.dist-info\\RECORD',
   'C:\\MyCode\\.venv\\lib\\site-packages\\click-8.2.0.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'C:\\MyCode\\.venv\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'C:\\MyCode\\.venv\\lib\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.1.dist-info\\RECORD',
   'C:\\MyCode\\.venv\\lib\\site-packages\\flask-3.1.1.dist-info\\RECORD',
   'DATA'),
  ('setuptools-63.2.0.dist-info\\INSTALLER',
   'C:\\MyCode\\.venv\\lib\\site-packages\\setuptools-63.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.5.dist-info\\LICENSE.txt',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy-2.2.5.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.5.dist-info\\RECORD',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy-2.2.5.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.5.dist-info\\WHEEL',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy-2.2.5.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'C:\\MyCode\\.venv\\lib\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'C:\\MyCode\\.venv\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'C:\\MyCode\\.venv\\lib\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'C:\\MyCode\\.venv\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.5.dist-info\\METADATA',
   'C:\\MyCode\\.venv\\lib\\site-packages\\numpy-2.2.5.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.0.dist-info\\WHEEL',
   'C:\\MyCode\\.venv\\lib\\site-packages\\click-8.2.0.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.0.dist-info\\INSTALLER',
   'C:\\MyCode\\.venv\\lib\\site-packages\\click-8.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-63.2.0.dist-info\\METADATA',
   'C:\\MyCode\\.venv\\lib\\site-packages\\setuptools-63.2.0.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.1.dist-info\\licenses\\LICENSE.txt',
   'C:\\MyCode\\.venv\\lib\\site-packages\\flask-3.1.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'C:\\MyCode\\.venv\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'C:\\MyCode\\.venv\\lib\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'C:\\MyCode\\build\\web_html_to_pdf\\base_library.zip',
   'DATA')],
 'python310.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
