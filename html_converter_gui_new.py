import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
from bs4 import BeautifulSoup
from xhtml2pdf import pisa
from PyPDF2 import PdfMerger

class HTMLConverterApp:
    def __init__(self, root):
        self.root = root
        self.root.title("HTML to PDF/TXT Converter")
        self.root.geometry("600x400")

        self.input_path = tk.StringVar()
        self.output_format = tk.StringVar(value="pdf")
        self.combine_files = tk.BooleanVar(value=False)
        self.output_dir = tk.StringVar()

        self.create_widgets()

    def create_widgets(self):
        # Input Section
        input_frame = ttk.LabelFrame(self.root, text="Input HTML Files/Directory")
        input_frame.pack(padx=10, pady=10, fill="x")

        ttk.Entry(input_frame, textvariable=self.input_path, width=60).grid(row=0, column=0, padx=5, pady=5)
        ttk.Button(input_frame, text="Browse Files", command=self.browse_input_files).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(input_frame, text="Browse Directory", command=self.browse_input_directory).grid(row=0, column=2, padx=5, pady=5)

        # Output Options
        output_frame = ttk.LabelFrame(self.root, text="Output Options")
        output_frame.pack(padx=10, pady=10, fill="x")

        ttk.Radiobutton(output_frame, text="Convert to PDF", variable=self.output_format, value="pdf").grid(row=0, column=0, padx=5, pady=5)
        ttk.Radiobutton(output_frame, text="Convert to TXT", variable=self.output_format, value="txt").grid(row=0, column=1, padx=5, pady=5)

        ttk.Checkbutton(output_frame, text="Combine into one file", variable=self.combine_files).grid(row=1, column=0, columnspan=2, padx=5, pady=5)

        # Output Directory
        output_dir_frame = ttk.LabelFrame(self.root, text="Output Directory")
        output_dir_frame.pack(padx=10, pady=10, fill="x")

        ttk.Entry(output_dir_frame, textvariable=self.output_dir, width=60).grid(row=0, column=0, padx=5, pady=5)
        ttk.Button(output_dir_frame, text="Browse", command=self.browse_output_directory).grid(row=0, column=1, padx=5, pady=5)

        # Convert Button
        ttk.Button(self.root, text="Convert", command=self.convert_files).pack(pady=10)

        # Progress Bar
        self.progress = ttk.Progressbar(self.root, orient="horizontal", length=500, mode="determinate")
        self.progress.pack(pady=5)

        self.status_label = ttk.Label(self.root, text="")
        self.status_label.pack(pady=5)

    def browse_input_files(self):
        files = filedialog.askopenfilenames(filetypes=[("HTML files", "*.html"), ("All files", "*.*")])
        if files:
            self.input_path.set(";".join(files))

    def browse_input_directory(self):
        directory = filedialog.askdirectory()
        if directory:
            self.input_path.set(directory)

    def browse_output_directory(self):
        directory = filedialog.askdirectory()
        if directory:
            self.output_dir.set(directory)

    def convert_files(self):
        input_path_str = self.input_path.get()
        output_dir_str = self.output_dir.get()
        output_format = self.output_format.get()
        combine = self.combine_files.get()

        if not input_path_str:
            messagebox.showwarning("Input Error", "Please select input HTML files or a directory.")
            return
        if not output_dir_str:
            messagebox.showwarning("Output Error", "Please select an output directory.")
            return

        html_files = []
        if os.path.isdir(input_path_str):
            for root, _, files in os.walk(input_path_str):
                for file in files:
                    if file.endswith(".html"):
                        html_files.append(os.path.join(root, file))
        else:
            html_files = input_path_str.split(";")

        # Sort HTML files numerically
        def sort_key(filepath):
            basename = os.path.basename(filepath)
            import re
            match = re.search(r'messages(\d*)\.html', basename)
            if match:
                num_str = match.group(1)
                return int(num_str) if num_str else 0
            return float('inf')

        html_files.sort(key=sort_key)

        if not html_files:
            messagebox.showwarning("No HTML Files", "No HTML files found in the selected input.")
            return

        self.progress["value"] = 0
        self.progress["maximum"] = len(html_files)
        self.status_label.config(text="Starting conversion...")

        converted_files = []
        for i, html_file in enumerate(html_files):
            try:
                self.status_label.config(text=f"Converting {os.path.basename(html_file)} ({i+1}/{len(html_files)})")
                self.root.update_idletasks()

                with open(html_file, "r", encoding="utf-8") as f:
                    html_content = f.read()

                base_name = os.path.splitext(os.path.basename(html_file))[0]
                output_filepath = os.path.join(output_dir_str, f"{base_name}.{output_format}")

                if output_format == "pdf":
                    self._convert_html_to_pdf(html_content, output_filepath)
                elif output_format == "txt":
                    self._convert_html_to_txt(html_content, output_filepath)
                converted_files.append(output_filepath)
            except Exception as e:
                messagebox.showerror("Conversion Error", f"Failed to convert {os.path.basename(html_file)}: {e}")
            finally:
                self.progress["value"] = i + 1

        if combine and converted_files:
            try:
                combined_output_filepath = os.path.join(output_dir_str, f"combined_output.{output_format}")
                self.status_label.config(text=f"Combining files into {os.path.basename(combined_output_filepath)}")
                self.root.update_idletasks()
                if output_format == "pdf":
                    self._combine_pdfs(converted_files, combined_output_filepath)
                elif output_format == "txt":
                    self._combine_txts(converted_files, combined_output_filepath)
                messagebox.showinfo("Conversion Complete", f"All files converted and combined successfully into {combined_output_filepath}!")
            except Exception as e:
                messagebox.showerror("Combination Error", f"Failed to combine files: {e}")
        elif converted_files:
            messagebox.showinfo("Conversion Complete", "All files converted successfully!")
        else:
            messagebox.showwarning("No Files Converted", "No files were converted.")

        self.status_label.config(text="Conversion finished.")

    def _convert_html_to_pdf(self, html_content, output_filepath):
        with open(output_filepath, "wb") as result_file:
            pisa_status = pisa.CreatePDF(
                html_content,
                dest=result_file,
                encoding="utf-8"
            )
        if pisa_status.err:
            raise Exception("PDF conversion error")

    def _convert_html_to_txt(self, html_content, output_filepath):
        soup = BeautifulSoup(html_content, 'html.parser')
        text_content = soup.get_text(separator='\n')
        with open(output_filepath, "w", encoding="utf-8") as f:
            f.write(text_content)

    def _combine_pdfs(self, pdf_files, output_filepath):
        merger = PdfMerger()
        for pdf in pdf_files:
            merger.append(pdf)
        merger.write(output_filepath)
        merger.close()

    def _combine_txts(self, txt_files, output_filepath):
        with open(output_filepath, "w", encoding="utf-8") as outfile:
            for fname in txt_files:
                with open(fname, "r", encoding="utf-8") as infile:
                    outfile.write(infile.read())
                    outfile.write("\n\n--- End of File ---\n\n")

if __name__ == "__main__":
    root = tk.Tk()
    app = HTMLConverterApp(root)
    root.mainloop()
