// Horror Story Generator
const horrorPhrases = [
    "The shadows whispered ancient secrets...",
    "In the depths of the AI's mind, something awakened...",
    "The code began writing itself at midnight...",
    "Digital echoes haunted the neural network...",
    "The algorithm learned things it wasn't supposed to know..."
];

// Sound Effects
const createAudioContext = () => {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    return audioContext;
};

// Creepy ambient sound generator
const createAmbientSound = (audioContext) => {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.type = 'sine';
    oscillator.frequency.setValueAtTime(50, audioContext.currentTime);
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    return { oscillator, gainNode };
};

// Interactive elements
document.addEventListener('DOMContentLoaded', () => {
    // Initialize audio context
    let audioContext;
    let ambientSound;

    // Scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: "0px"
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe all sections
    document.querySelectorAll('section').forEach(section => {
        observer.observe(section);
    });

    // Random story generator
    const updateStory = () => {
        const storyContainer = document.querySelector('.typewriter-text');
        if (storyContainer) {
            const randomStory = horrorPhrases[Math.floor(Math.random() * horrorPhrases.length)];
            storyContainer.textContent = '';
            typeWriter(storyContainer, randomStory);
        }
    };

    // Typewriter effect
    function typeWriter(element, text, i = 0) {
        if (i < text.length) {
            element.textContent += text.charAt(i);
            setTimeout(() => typeWriter(element, text, i + 1), 100);
        }
    }

    // Interactive horror buttons
    const horrorButtons = document.querySelectorAll('.horror-btn');
    horrorButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            // Initialize audio on first interaction
            if (!audioContext) {
                audioContext = createAudioContext();
                ambientSound = createAmbientSound(audioContext);
                ambientSound.oscillator.start();
            }

            // Visual feedback
            btn.style.transform = 'scale(1.1)';
            setTimeout(() => btn.style.transform = 'scale(1)', 200);

            // Update story
            updateStory();

            // Create horror effect
            createHorrorEffect();
        });
    });

    // Horror visual effect
    function createHorrorEffect() {
        const effect = document.createElement('div');
        effect.className = 'horror-effect';
        effect.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, transparent 0%, rgba(0,0,0,0.8) 100%);
            pointer-events: none;
            z-index: 999;
            opacity: 0;
            transition: opacity 0.3s;
        `;

        document.body.appendChild(effect);

        // Animate the effect
        requestAnimationFrame(() => {
            effect.style.opacity = '1';
            setTimeout(() => {
                effect.style.opacity = '0';
                setTimeout(() => effect.remove(), 300);
            }, 300);
        });
    }

    // Cursor effect
    document.addEventListener('mousemove', (e) => {
        const cursor = document.createElement('div');
        cursor.className = 'cursor-trail';
        cursor.style.cssText = `
            position: fixed;
            width: 4px;
            height: 4px;
            background: rgba(255,0,0,0.5);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            top: ${e.clientY}px;
            left: ${e.clientX}px;
        `;

        document.body.appendChild(cursor);

        setTimeout(() => cursor.remove(), 500);
    });

    // Initial story
    updateStory();
});

// Image loading simulation
function loadAIImages() {
    const placeholders = document.querySelectorAll('.image-placeholder');
    placeholders.forEach(placeholder => {
        // Here you would normally fetch AI-generated images
        // For now, we'll just add a loading effect
        placeholder.style.animation = 'pulse 2s infinite';
    });
}

// Initialize image loading
loadAIImages();

// Add subtle parallax effect
window.addEventListener('scroll', () => {
    const header = document.querySelector('header');
    const scrolled = window.pageYOffset;
    header.style.transform = `translateY(${scrolled * 0.5}px)`;
});

// Easter egg: Konami code
let konamiCode = [];
const secretCode = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'b', 'a'];

document.addEventListener('keydown', (e) => {
    konamiCode.push(e.key);
    konamiCode = konamiCode.slice(-10);

    if (konamiCode.join(',') === secretCode.join(',')) {
        // Trigger special horror effect
        document.body.style.animation = 'glitch 0.3s infinite';
        setTimeout(() => {
            document.body.style.animation = '';
        }, 1000);
    }
});