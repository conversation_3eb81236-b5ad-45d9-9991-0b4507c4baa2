# 🔄 HTML Bulk Converter

A beautiful GUI application that converts bulk HTML files to PDF or TXT format with perfect content preservation and proper numerical ordering.

## ✨ Features

- **Beautiful Dark Theme GUI** - Modern, user-friendly interface
- **Perfect Content Preservation** - No text loss, all messages preserved exactly as they are
- **Numerical Ordering** - Processes files in correct order: file1.html → file2.html → file3.html...
- **Bulk Conversion** - Convert multiple files at once
- **Combine Files** - Option to merge all files into one document
- **Multiple Formats** - Export to PDF (recommended) or TXT
- **Progress Tracking** - Real-time conversion progress
- **Smart Sorting** - Automatically handles numerical file naming patterns

## 🚀 Quick Start

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the Application**
   ```bash
   python html_converter.py
   ```

3. **Convert Your Files**
   - Select your HTML files or folder containing HTML files
   - Choose output format (PDF recommended)
   - Select output directory
   - Click "Start Conversion"

## 📁 Perfect for Various HTML Sources

This tool works great with:
- Chat exports (Telegram, WhatsApp, etc.)
- Documentation files
- Web pages saved as HTML
- Any HTML content you want to convert

## 🎯 Smart File Processing

The application handles various file naming patterns:
- `messages.html` (first file)
- `messages2.html`
- `messages3.html`
- `file1.html`, `file2.html`, etc.
- Any numerical sequence

The application automatically sorts and processes them in the correct numerical order, ensuring your content flows naturally from first to last file.

## 💡 Tips

- **PDF Format**: Recommended for better formatting and readability
- **Combine Files**: Creates a single document with all content
- **File Order**: Files are automatically sorted numerically (file1.html, file2.html, etc.)
- **Content Preservation**: All text, formatting, and structure preserved exactly

## 🔧 Requirements

- Python 3.7+
- tkinter (usually included with Python)
- beautifulsoup4
- reportlab (for PDF conversion)
- PyPDF2 (for PDF combining)

## 📝 Output Examples

### TXT Format
```
HTML Content Export
==================================================

Content from your HTML files converted to clean text format
with all original text preserved exactly as it appears.
```

### PDF Format
Beautiful formatted PDF with proper styling, timestamps, and message structure preserved.

## 🎨 Interface Preview

The application features:
- 🗂️ File selection with drag-and-drop support
- 📄 Format selection (PDF/TXT)
- 💾 Output directory selection
- ⚙️ Conversion options
- 📊 Real-time progress tracking
- 🚀 One-click conversion

## 🆘 Support

If you encounter any issues:
1. Make sure all dependencies are installed
2. Check that your HTML files are valid HTML format
3. Ensure you have write permissions to the output directory
4. Use the built-in Help button for more information

---

**Made with ❤️ for converting your HTML content beautifully!**