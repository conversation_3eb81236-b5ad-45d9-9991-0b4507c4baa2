<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Dynamic Telegram Chat Renderer</title>
<style>
  body {
    font-family: Arial, sans-serif;
    background-color: #e5ddd5;
    margin: 0;
    padding: 0;
  }
  .chat-container {
    max-width: 600px;
    margin: 20px auto;
    background: #fff;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 0 5px rgba(0,0,0,0.1);
  }
  .message {
    margin: 10px 0;
    padding: 8px 12px;
    border-radius: 10px;
    max-width: 80%;
    clear: both;
    position: relative;
    font-size: 14px;
    line-height: 1.4;
    page-break-inside: avoid;
  }
  .message .date {
    font-size: 10px;
    color: #999;
    margin-bottom: 4px;
  }
  .message .text {
    white-space: pre-wrap;
  }
  .message.left {
    background-color: #f1f0f0;
    float: left;
    border-top-left-radius: 0;
  }
  .message.right {
    background-color: #dcf8c6;
    float: right;
    border-top-right-radius: 0;
  }
  .user-name {
    font-weight: bold;
    margin-bottom: 2px;
  }
  .date-separator {
    text-align: center;
    margin: 15px 0;
    font-size: 12px;
    color: #666;
    clear: both;
    page-break-before: always;
  }

</style>
</head>
<body>
<div class="chat-container" id="chatContainer"></div>

<script>
  // Sample chat data
  const chatData = [
    { user: "User 1", datetime: "2023-03-20T20:00:00", text: "Hello" },
    { user: "User 2", datetime: "2023-03-20T20:01:00", text: "Hi" },
    { user: "User 1", datetime: "2023-03-20T20:02:00", text: "How are you?" },
    { user: "User 2", datetime: "2023-03-20T20:03:00", text: "I'm good, thanks! And you?" },
    { user: "User 1", datetime: "2023-03-20T20:04:00", text: "Doing well, thanks for asking." },
    { user: "User 2", datetime: "2023-03-20T20:05:00", text: "Great to hear!" }
  ];

  // Function to format date as "Monday, 20 March 2023"
  function formatDate(date) {
    return date.toLocaleDateString(undefined, { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
  }

  // Function to format time as "HH:mm"
  function formatTime(date) {
    return date.toLocaleTimeString(undefined, { hour: '2-digit', minute: '2-digit', hour12: false });
  }

  // Render chat messages dynamically
  function renderChat(chatData) {
    const container = document.getElementById('chatContainer');
    container.innerHTML = '';

    let lastDate = null;

    chatData.forEach(msg => {
      const msgDate = new Date(msg.datetime);
      const msgDateStr = formatDate(msgDate);

      // Insert date separator if date changes
      if (lastDate !== msgDateStr) {
        const dateSeparator = document.createElement('div');
        dateSeparator.className = 'date-separator';
        dateSeparator.textContent = msgDateStr;
        container.appendChild(dateSeparator);
        lastDate = msgDateStr;
      }

      // Create message element
      const messageDiv = document.createElement('div');
      messageDiv.className = 'message ' + (msg.user === 'User 1' ? 'left' : 'right');

      // User name
      const userNameDiv = document.createElement('div');
      userNameDiv.className = 'user-name';
      userNameDiv.textContent = msg.user;
      messageDiv.appendChild(userNameDiv);

      // Time
      const timeDiv = document.createElement('div');
      timeDiv.className = 'date';
      timeDiv.textContent = formatTime(msgDate);
      messageDiv.appendChild(timeDiv);

      // Text
      const textDiv = document.createElement('div');
      textDiv.className = 'text';
      textDiv.textContent = msg.text;
      messageDiv.appendChild(textDiv);

      container.appendChild(messageDiv);
    });
  }

  // Initialize rendering
  renderChat(chatData);
</script>
</body>
</html>
