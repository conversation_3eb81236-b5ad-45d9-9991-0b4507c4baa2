/* Dark Theme Base Styles */
:root {
    --dark-bg: #0a0a0a;
    --darker-bg: #000000;
    --accent-color: #ff0000;
    --text-color: #ffffff;
    --shadow-color: rgba(255, 0, 0, 0.2);
}

body {
    background-color: var(--dark-bg);
    color: var(--text-color);
    font-family: 'Times New Roman', serif;
    margin: 0;
    padding: 0;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Fog Effect */
.fog-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.fog {
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 100vh;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABHNCSVQICAgIfAhkiAAAAWJJREFUeJzt1lEKwjAQBcC8/6Fr8ApCD9UvY1HD7gzMx8BA2yR9rLWOc85551prjTHGWmvNOeece13XtdZ6nOePc/5za63HnHPOOeeca6211mOttR5zzjnnnHPOOeece17XdY2ZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZt9sD1JXAx5wB+GwAAAABJRU5ErkJggg==');
    animation: fog 60s linear infinite;
    opacity: 0.5;
}

@keyframes fog {
    0% { transform: translateX(0); }
    100% { transform: translateX(-50%); }
}

/* Header Styles */
header {
    text-align: center;
    padding: 4rem 2rem;
    position: relative;
    z-index: 2;
}

.glitch-text {
    font-size: 4rem;
    text-shadow: 
        0.05em 0 0 var(--shadow-color),
        -0.025em -0.05em 0 rgba(0, 255, 0, 0.2),
        0.025em 0.05em 0 rgba(0, 0, 255, 0.2);
    animation: glitch 1s infinite;
    letter-spacing: 0.2em;
    margin: 0;
}

@keyframes glitch {
    0% { text-shadow: 0.05em 0 0 var(--shadow-color); }
    15% { text-shadow: -0.05em -0.025em 0 var(--shadow-color); }
    49% { text-shadow: 0.05em 0.025em 0 var(--shadow-color); }
    50% { text-shadow: 0.05em 0 0 var(--shadow-color); }
    100% { text-shadow: -0.05em 0 0 var(--shadow-color); }
}

.subtitle {
    font-style: italic;
    opacity: 0.7;
    margin-top: 1rem;
}

/* Navigation */
nav {
    background-color: var(--darker-bg);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    padding: 0;
    margin: 0;
}

nav li {
    margin: 0 2rem;
}

nav a {
    color: var(--text-color);
    text-decoration: none;
    font-size: 1.2rem;
    transition: color 0.3s;
}

nav a:hover {
    color: var(--accent-color);
}

/* Main Content */
main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    position: relative;
    z-index: 2;
}

section {
    margin-bottom: 4rem;
}

/* Gallery Styles */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    padding: 2rem 0;
}

.gallery-item {
    aspect-ratio: 1;
    background-color: var(--darker-bg);
    position: relative;
    overflow: hidden;
    transition: transform 0.3s;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.image-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #1a1a1a, #2a2a2a);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

/* Interactive Elements */
.horror-btn {
    background-color: var(--darker-bg);
    color: var(--text-color);
    border: 1px solid var(--accent-color);
    padding: 1rem 2rem;
    margin: 1rem;
    cursor: pointer;
    transition: all 0.3s;
}

.horror-btn:hover {
    background-color: var(--accent-color);
    transform: scale(1.05);
}

/* Story Section */
.story-container {
    background-color: var(--darker-bg);
    padding: 2rem;
    border-left: 4px solid var(--accent-color);
}

.typewriter-text {
    overflow: hidden;
    border-right: 0.15em solid var(--accent-color);
    white-space: nowrap;
    margin: 0;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0 }
    to { width: 100% }
}

@keyframes blink-caret {
    from, to { border-color: transparent }
    50% { border-color: var(--accent-color) }
}

/* Fade-in Animation */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeIn 1s ease-out forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Footer */
footer {
    text-align: center;
    padding: 2rem;
    background-color: var(--darker-bg);
    position: relative;
    z-index: 2;
}

/* Responsive Design */
@media (max-width: 768px) {
    .glitch-text {
        font-size: 2.5rem;
    }

    nav ul {
        flex-direction: column;
        align-items: center;
    }

    nav li {
        margin: 0.5rem 0;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}