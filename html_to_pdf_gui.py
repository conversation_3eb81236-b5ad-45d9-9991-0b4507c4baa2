import os
import threading
from tkinter import Tk, <PERSON>, <PERSON><PERSON>, filedialog, messagebox
from playwright.sync_api import sync_playwright
from PyPDF2 import PdfMerger

class HtmlToPdfConverterApp:
    def __init__(self, root):
        self.root = root
        self.root.title("HTML to PDF Converter")
        self.root.geometry("400x200")

        self.label = Label(root, text="Select folder containing HTML files")
        self.label.pack(pady=10)

        self.select_button = But<PERSON>(root, text="Select Folder", command=self.select_folder)
        self.select_button.pack(pady=5)

        self.convert_button = Button(root, text="Convert and Merge PDFs", command=self.convert_and_merge, state="disabled")
        self.convert_button.pack(pady=5)

        self.status_label = Label(root, text="")
        self.status_label.pack(pady=10)

        self.folder_path = None

    def select_folder(self):
        folder_selected = filedialog.askdirectory()
        if folder_selected:
            self.folder_path = folder_selected
            self.label.config(text=f"Selected folder: {folder_selected}")
            self.convert_button.config(state="normal")

    def convert_and_merge(self):
        if not self.folder_path:
            messagebox.showerror("Error", "Please select a folder first.")
            return
        self.convert_button.config(state="disabled")
        self.status_label.config(text="Converting HTML files to PDFs...")
        threading.Thread(target=self._convert_and_merge_thread).start()

    def _convert_and_merge_thread(self):
        try:
            pdf_files = []
            with sync_playwright() as p:
                browser = p.chromium.launch()
                for i in range(1, 28):
                    html_file = os.path.join(self.folder_path, f"messages{i if i > 1 else ''}.html")
                    pdf_file = os.path.join(self.folder_path, f"messages{i if i > 1 else ''}.pdf")
                    if os.path.exists(html_file):
                        self._update_status(f"Converting {html_file}...")
                        page = browser.new_page()
                        page.goto(f"file://{html_file}")
                        page.pdf(path=pdf_file)
                        page.close()
                        pdf_files.append(pdf_file)
                    else:
                        self._update_status(f"File not found: {html_file}")
                browser.close()

            self._update_status("Merging PDFs...")
            merger = PdfMerger()
            for pdf in pdf_files:
                merger.append(pdf)
            output_pdf = os.path.join(self.folder_path, "merged_output.pdf")
            merger.write(output_pdf)
            merger.close()
            self._update_status(f"Conversion and merge completed! Output: {output_pdf}")
            messagebox.showinfo("Success", f"PDFs merged successfully!\nOutput file: {output_pdf}")
        except Exception as e:
            self._update_status(f"Error: {str(e)}")
            messagebox.showerror("Error", str(e))
        finally:
            self.convert_button.config(state="normal")

    def _update_status(self, message):
        def update():
            self.status_label.config(text=message)
        self.root.after(0, update)

if __name__ == "__main__":
    root = Tk()
    app = HtmlToPdfConverterApp(root)
    root.mainloop()
