#!/usr/bin/env python3
"""
Test script to verify the natural sorting functionality for Telegram HTML files
"""

import os
import re

def natural_sort_key(filename):
    """Natural sorting for filenames with numbers - handles messages.html, messages2.html, etc."""
    # Extract the base name without extension
    base_name = os.path.splitext(filename)[0].lower()

    # Handle special case for messages.html (should be first)
    if base_name == 'messages':
        return (0, 0)

    # Handle messagesN.html pattern
    if base_name.startswith('messages'):
        try:
            # Extract number after 'messages'
            number_part = base_name[8:]  # Remove 'messages' prefix
            if number_part.isdigit():
                return (0, int(number_part))
        except:
            pass

    # Fallback to general natural sorting for other files
    # Split filename into parts and convert numbers to integers
    parts = re.split('([0-9]+)', filename.lower())
    result = []
    for part in parts:
        if part.isdigit():
            result.append(int(part))
        else:
            result.append(part)
    return (1, result)  # Other files come after messages files

def test_sorting():
    """Test the sorting with sample filenames"""
    test_files = [
        'messages27.html',
        'messages.html',
        'messages3.html',
        'messages10.html',
        'messages2.html',
        'messages15.html',
        'messages5.html',
        'messages20.html',
        'messages1.html',
        'other_file.html'
    ]
    
    print("Original order:")
    for i, filename in enumerate(test_files, 1):
        print(f"{i:2d}. {filename}")
    
    print("\nSorted order (correct chronological order):")
    sorted_files = sorted(test_files, key=natural_sort_key)
    for i, filename in enumerate(sorted_files, 1):
        print(f"{i:2d}. {filename}")
    
    print("\nExpected order should be:")
    print("1. messages.html")
    print("2. messages2.html") 
    print("3. messages3.html")
    print("4. messages5.html")
    print("5. messages10.html")
    print("6. messages15.html")
    print("7. messages20.html")
    print("8. messages27.html")
    print("9. other_file.html")

if __name__ == "__main__":
    test_sorting()