# Horror Mystery AI Website 🕯️👻

An interactive horror-themed website featuring AI-generated content, mysterious elements, and atmospheric effects.

## Features 🦇

- Dark, atmospheric design with fog effects
- Interactive horror elements
- Dynamic story generation
- Mysterious AI-generated imagery
- Responsive design for all devices
- Interactive sound effects
- Hidden easter eggs
- Parallax scrolling effects
- Typewriter text animations
- Cursor trail effects

## Setup Instructions 💀

1. Clone this repository
2. Open `index.html` in a modern web browser
3. Enable JavaScript for full interactive features
4. Enable sound for the complete horror experience

## Project Structure 🏚️

```
HorrorMysteryAI/
├── index.html          # Main entry point
├── css/
│   └── style.css      # Styling and animations
├── js/
│   └── horror.js      # Interactive features
└── images/            # Static assets and AI-generated images
```

## Interactive Elements 🕸️

- Click the horror buttons to trigger effects
- Scroll to reveal animated content
- Watch for mysterious messages
- Try the Konami code for a surprise
- Interact with the gallery
- Follow the whispers...

## Technical Features ⚡

- CSS3 Animations and Transitions
- JavaScript ES6+
- Responsive Design
- Dynamic Content Generation
- Web Audio API for Sound Effects
- Intersection Observer API
- Custom Cursor Effects
- Random Story Generation

## Browser Support 🌙

- Chrome (recommended for best experience)
- Firefox
- Safari
- Edge

## Adding AI-Generated Images 🖼️

To add your own AI-generated horror images:

1. Place images in the `images` folder
2. Update the image paths in `horror.js`
3. Maintain the dark theme aesthetic

## Known Issues 🪦

- Sound may not autoplay (browser security)
- Some effects may be CPU-intensive
- AI image generation requires setup

## Contributing 🩸

Feel free to contribute to this project:

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License 📜

This project is open source and available under the MIT License.

## Disclaimer ⚠️

Some effects may be intense. User discretion is advised.

---
Created with 🖤 by CodeGeeX