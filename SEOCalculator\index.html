<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Calculator Pro - Advanced SEO Analysis Tools</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #f8fafc;
        --accent-color: #10b981;
        --error-color: #ef4444;
        --text-color: #334155;
    }

    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: var(--text-color);
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        min-height: 100vh;
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23a0aec0' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .header {
        background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23fff' fill-opacity='0.05' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
    }

    .header h1 {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        position: relative;
    }

    .card {
        border: none;
        border-radius: 15px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 48px rgba(0, 0, 0, 0.12);
    }

    .nav-tabs .nav-link {
        color: var(--text-color);
        border: none;
        padding: 1rem 1.5rem;
        transition: all 0.3s ease;
        border-radius: 8px 8px 0 0;
    }

    .nav-tabs .nav-link:hover {
        background: rgba(37, 99, 235, 0.1);
    }

    .nav-tabs .nav-link.active {
        color: var(--primary-color);
        border-bottom: 3px solid var(--primary-color);
        font-weight: 600;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
        border: none;
        padding: 0.8rem 1.8rem;
        border-radius: 30px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #1d4ed8, #1e40af);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(37, 99, 235, 0.3);
    }

    .result-card {
        background: var(--secondary-color);
        border-radius: 12px;
        padding: 2rem;
        margin-top: 1.5rem;
        animation: fadeIn 0.5s ease-out;
    }

    .score-circle {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        font-weight: bold;
        margin: 1rem auto;
        background: conic-gradient(var(--accent-color) var(--percentage), #e2e8f0 0);
        box-shadow: inset 0 0 0 8px rgba(16, 185, 129, 0.1);
        position: relative;
        transition: all 0.3s ease;
    }

    .score-circle::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 80%;
        height: 80%;
        background: white;
        border-radius: 50%;
    }

    .score-circle span {
        position: relative;
        z-index: 1;
    }

    .feature-icon {
        font-size: 2.5rem;
        color: var(--primary-color);
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .feature-icon:hover {
        transform: scale(1.1);
    }

    .progress {
        height: 0.8rem;
        border-radius: 1rem;
        background: #e2e8f0;
        overflow: hidden;
    }

    .progress-bar {
        transition: width 1s ease-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes scaleIn {
        from { transform: scale(0.95); }
        to { transform: scale(1); }
    }

    .highlight {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .form-control {
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        padding: 0.8rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }

    .tab-content {
        animation: fadeIn 0.3s ease-out;
    }
</style>
</head>
<body>
    <div class="header text-center">
        <div class="container">
            <h1 class="display-4">SEO Calculator Pro</h1>
            <p class="lead">Advanced SEO Analysis & Optimization Tools</p>
        </div>
    </div>

    <div class="container mb-5">
        <div class="card">
            <div class="card-body">
                <ul class="nav nav-tabs mb-4" id="seoTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="content-tab" data-bs-toggle="tab" href="#content" role="tab">
                            <i class="bi bi-file-text"></i> Content Analysis
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="keyword-tab" data-bs-toggle="tab" href="#keyword" role="tab">
                            <i class="bi bi-key"></i> Keyword Density
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="meta-tab" data-bs-toggle="tab" href="#meta" role="tab">
                            <i class="bi bi-card-text"></i> Meta Tags
                        </a>
                    </li>
                </ul>

                <div class="tab-content" id="seoTabsContent">
                    <!-- Content Analysis Tab -->
                    <div class="tab-pane fade show active" id="content" role="tabpanel">
                        <div class="mb-4">
                            <label for="contentText" class="form-label">Paste your content here:</label>
                            <textarea class="form-control" id="contentText" rows="6" placeholder="Enter your content to analyze..."></textarea>
                        </div>
                        <button class="btn btn-primary" onclick="analyzeContent()">
                            <i class="bi bi-search"></i> Analyze Content
                        </button>
                        <div id="contentResults" class="result-card mt-4" style="display: none;">
                            <h4>Content Analysis Results</h4>
                            <div class="row mt-3">
                                <div class="col-md-3 text-center">
                                    <div class="feature-icon">
                                        <i class="bi bi-text-paragraph"></i>
                                    </div>
                                    <h5>Word Count</h5>
                                    <p id="wordCount">0 words</p>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="feature-icon">
                                        <i class="bi bi-clock"></i>
                                    </div>
                                    <h5>Reading Time</h5>
                                    <p id="readingTime">0 min</p>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="feature-icon">
                                        <i class="bi bi-graph-up"></i>
                                    </div>
                                    <h5>Readability</h5>
                                    <p id="readabilityScore">N/A</p>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="feature-icon">
                                        <i class="bi bi-check-circle"></i>
                                    </div>
                                    <h5>SEO Score</h5>
                                    <p id="seoScore">0%</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Keyword Density Tab -->
                    <div class="tab-pane fade" id="keyword" role="tabpanel">
                        <div class="mb-4">
                            <label for="keywordText" class="form-label">Content for keyword analysis:</label>
                            <textarea class="form-control mb-3" id="keywordText" rows="6" placeholder="Paste your content here..."></textarea>
                            <label for="targetKeyword" class="form-label">Target Keyword:</label>
                            <input type="text" class="form-control" id="targetKeyword" placeholder="Enter your target keyword">
                        </div>
                        <button class="btn btn-primary" onclick="analyzeKeywordDensity()">
                            <i class="bi bi-calculator"></i> Calculate Density
                        </button>
                        <div id="keywordResults" class="result-card mt-4" style="display: none;">
                            <h4>Keyword Analysis Results</h4>
                            <div class="row mt-3">
                                <div class="col-md-4 text-center">
                                    <div class="score-circle" id="densityScore">
                                        0%
                                    </div>
                                    <h5>Keyword Density</h5>
                                </div>
                                <div class="col-md-4">
                                    <h5>Keyword Occurrences</h5>
                                    <p id="keywordCount">0 times</p>
                                </div>
                                <div class="col-md-4">
                                    <h5>Optimization Tips</h5>
                                    <p id="densityTips"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Meta Tags Tab -->
                    <div class="tab-pane fade" id="meta" role="tabpanel">
                        <div class="mb-4">
                            <label for="metaTitle" class="form-label">Meta Title:</label>
                            <input type="text" class="form-control mb-3" id="metaTitle" placeholder="Enter meta title">

                            <label for="metaDescription" class="form-label">Meta Description:</label>
                            <textarea class="form-control" id="metaDescription" rows="3" placeholder="Enter meta description"></textarea>
                        </div>
                        <button class="btn btn-primary" onclick="analyzeMeta()">
                            <i class="bi bi-check-square"></i> Analyze Meta Tags
                        </button>
                        <div id="metaResults" class="result-card mt-4" style="display: none;">
                            <h4>Meta Tags Analysis</h4>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h5>Title Analysis</h5>
                                    <div class="progress mb-2">
                                        <div class="progress-bar" id="titleProgress" role="progressbar"></div>
                                    </div>
                                    <p id="titleAnalysis"></p>
                                </div>
                                <div class="col-md-6">
                                    <h5>Description Analysis</h5>
                                    <div class="progress mb-2">
                                        <div class="progress-bar" id="descriptionProgress" role="progressbar"></div>
                                    </div>
                                    <p id="descriptionAnalysis"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Content Analysis
        function analyzeContent() {
            const content = document.getElementById('contentText').value;
            if (!content) {
                alert('Please enter some content to analyze');
                return;
            }

            const words = content.trim().split(/\s+/).length;
            const readingTime = Math.ceil(words / 200); // Assuming 200 words per minute
            const sentences = content.split(/[.!?]+/).length;
            const averageWordsPerSentence = words / sentences;

            // Calculate readability score (simplified)
            let readabilityScore = 'Easy';
            if (averageWordsPerSentence > 20) {
                readabilityScore = 'Complex';
            } else if (averageWordsPerSentence > 14) {
                readabilityScore = 'Moderate';
            }

            // Calculate SEO score (simplified)
            let seoScore = 0;
            seoScore += words > 300 ? 30 : Math.round((words/300) * 30);
            seoScore += sentences > 10 ? 30 : Math.round((sentences/10) * 30);
            seoScore += averageWordsPerSentence < 20 ? 40 : Math.round((20/averageWordsPerSentence) * 40);

            document.getElementById('contentResults').style.display = 'block';
            document.getElementById('wordCount').textContent = `${words} words`;
            document.getElementById('readingTime').textContent = `${readingTime} min`;
            document.getElementById('readabilityScore').textContent = readabilityScore;
            document.getElementById('seoScore').textContent = `${seoScore}%`;
        }

        // Keyword Density Analysis
        function analyzeKeywordDensity() {
            const content = document.getElementById('keywordText').value.toLowerCase();
            const keyword = document.getElementById('targetKeyword').value.toLowerCase();

            if (!content || !keyword) {
                alert('Please enter both content and keyword');
                return;
            }

            const words = content.trim().split(/\s+/);
            const keywordCount = content.split(keyword).length - 1;
            const density = (keywordCount / words.length * 100).toFixed(2);

            let tips = '';
            if (density < 0.5) {
                tips = 'Consider increasing keyword usage';
            } else if (density > 2.5) {
                tips = 'Warning: Possible keyword stuffing';
            } else {
                tips = 'Keyword density is optimal';
            }

            document.getElementById('keywordResults').style.display = 'block';
            document.getElementById('densityScore').textContent = `${density}%`;
            document.getElementById('densityScore').style.setProperty('--percentage', `${density * 36}deg`);
            document.getElementById('keywordCount').textContent = `${keywordCount} times`;
            document.getElementById('densityTips').textContent = tips;
        }

        // Meta Tags Analysis
        function analyzeMeta() {
            const title = document.getElementById('metaTitle').value;
            const description = document.getElementById('metaDescription').value;

            if (!title && !description) {
                alert('Please enter at least one meta tag to analyze');
                return;
            }

            const titleLength = title.length;
            const descriptionLength = description.length;

            // Title analysis
            let titleScore = 0;
            let titleAnalysis = '';
            if (titleLength === 0) {
                titleAnalysis = 'Title is missing!';
            } else if (titleLength < 30) {
                titleScore = 50;
                titleAnalysis = 'Title is too short (recommended: 50-60 characters)';
            } else if (titleLength > 60) {
                titleScore = 70;
                titleAnalysis = 'Title is too long (recommended: 50-60 characters)';
            } else {
                titleScore = 100;
                titleAnalysis = 'Title length is perfect!';
            }

            // Description analysis
            let descriptionScore = 0;
            let descriptionAnalysis = '';
            if (descriptionLength === 0) {
                descriptionAnalysis = 'Description is missing!';
            } else if (descriptionLength < 120) {
                descriptionScore = 50;
                descriptionAnalysis = 'Description is too short (recommended: 120-155 characters)';
            } else if (descriptionLength > 155) {
                descriptionScore = 70;
                descriptionAnalysis = 'Description is too long (recommended: 120-155 characters)';
            } else {
                descriptionScore = 100;
                descriptionAnalysis = 'Description length is perfect!';
            }

            document.getElementById('metaResults').style.display = 'block';
            document.getElementById('titleProgress').style.width = `${titleScore}%`;
            document.getElementById('titleProgress').className = `progress-bar bg-${titleScore === 100 ? 'success' : titleScore >= 70 ? 'warning' : 'danger'}`;
            document.getElementById('titleAnalysis').textContent = titleAnalysis;

            document.getElementById('descriptionProgress').style.width = `${descriptionScore}%`;
            document.getElementById('descriptionProgress').className = `progress-bar bg-${descriptionScore === 100 ? 'success' : descriptionScore >= 70 ? 'warning' : 'danger'}`;
            document.getElementById('descriptionAnalysis').textContent = descriptionAnalysis;
        }
    </script>
</body>
</html>