#!/usr/bin/env python3
"""
Build script to create executable for HTML Bulk Converter
"""

import os
import sys
import subprocess
import shutil

def install_pyinstaller():
    """Install PyInstaller if not already installed"""
    try:
        import PyInstaller
        print("✅ PyInstaller is already installed")
    except ImportError:
        print("📦 Installing PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller installed successfully")

def build_executable():
    """Build the executable using PyInstaller"""
    print("🔨 Building executable...")
    
    # PyInstaller command
    cmd = [
        "pyinstaller",
        "--onefile",                    # Create a single executable file
        "--windowed",                   # Hide console window
        "--name=HTMLBulkConverter",     # Name of the executable
        "--icon=NONE",                  # No icon for now
        "--add-data=requirements.txt;.", # Include requirements file
        "--hidden-import=tkinter",      # Ensure tkinter is included
        "--hidden-import=tkinter.ttk",  # Ensure ttk is included
        "--hidden-import=bs4",          # BeautifulSoup
        "--hidden-import=reportlab",    # ReportLab
        "--hidden-import=PyPDF2",       # PyPDF2
        "--hidden-import=reportlab.platypus",
        "--hidden-import=reportlab.lib.pagesizes",
        "--hidden-import=reportlab.lib.styles",
        "--hidden-import=reportlab.lib.colors",
        "--hidden-import=reportlab.lib.units",
        "html_converter.py"             # Main script
    ]
    
    try:
        subprocess.check_call(cmd)
        print("✅ Executable built successfully!")
        
        # Check if the executable was created
        exe_path = os.path.join("dist", "HTMLBulkConverter.exe")
        if os.path.exists(exe_path):
            print(f"📁 Executable location: {os.path.abspath(exe_path)}")
            print(f"📏 File size: {os.path.getsize(exe_path) / (1024*1024):.1f} MB")
        else:
            print("❌ Executable not found in expected location")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        return False
    
    return True

def cleanup_build_files():
    """Clean up temporary build files"""
    print("🧹 Cleaning up build files...")
    
    # Remove build directory
    if os.path.exists("build"):
        shutil.rmtree("build")
        print("✅ Removed build directory")
    
    # Remove spec file
    spec_file = "HTMLBulkConverter.spec"
    if os.path.exists(spec_file):
        os.remove(spec_file)
        print("✅ Removed spec file")

def create_batch_file():
    """Create a batch file to run the executable"""
    batch_content = """@echo off
title HTML Bulk Converter
color 0B
cls

echo ========================================
echo  HTML Bulk Converter
echo  Beautiful HTML to PDF/TXT Tool
echo ========================================
echo.

echo Starting application...
echo.

start "" "%~dp0HTMLBulkConverter.exe"
"""
    
    with open(os.path.join("dist", "Run_HTMLConverter.bat"), "w") as f:
        f.write(batch_content)
    print("✅ Created batch file: Run_HTMLConverter.bat")

def main():
    """Main build process"""
    print("🚀 HTML Bulk Converter - Executable Builder")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("html_converter.py"):
        print("❌ Error: html_converter.py not found!")
        print("Please run this script from the HTMLBulkConverter directory")
        return
    
    # Install PyInstaller
    install_pyinstaller()
    
    # Build executable
    if build_executable():
        # Create batch file
        create_batch_file()
        
        # Clean up
        cleanup_build_files()
        
        print("\n🎉 Build completed successfully!")
        print("📁 Your executable is in the 'dist' folder")
        print("💡 You can now distribute HTMLBulkConverter.exe")
        print("\n📋 What's included:")
        print("   • All Python dependencies")
        print("   • Beautiful GUI interface")
        print("   • PDF and TXT conversion")
        print("   • No Python installation required")
        print("\n🚀 To run:")
        print("   • Double-click HTMLBulkConverter.exe")
        print("   • Or use Run_HTMLConverter.bat")
    else:
        print("\n❌ Build failed. Please check the error messages above.")

if __name__ == "__main__":
    main()