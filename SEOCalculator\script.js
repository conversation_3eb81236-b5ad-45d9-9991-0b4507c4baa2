// SEO Calculator Pro - JavaScript Functions

// Content Analysis Functions
function analyzeContent() {
    const content = document.querySelector('.content-area').value;
    if (!content.trim()) {
        showToast('Please enter some content to analyze', 'warning');
        return;
    }

    // Show loading state
    showLoading();

    // Basic metrics
    const wordCount = getWordCount(content);
    const readingTime = calculateReadingTime(wordCount);
    const readabilityScore = calculateReadabilityScore(content);
    const seoScore = calculateSEOScore(content);

    // Advanced metrics
    const keywordDensity = calculateKeywordDensity(content);
    const headingStructure = analyzeHeadingStructure(content);
    const linkAnalysis = analyzeLinkStructure(content);

    // Update UI with animations
    setTimeout(() => {
        hideLoading();
        updateMetrics({
            wordCount,
            readingTime,
            readabilityScore,
            seoScore,
            keywordDensity,
            headingStructure,
            linkAnalysis
        });
        createCharts(seoScore, keywordDensity);
    }, 1000);
}

// Keyword Density Analysis
function analyzeKeywords() {
    const content = document.querySelector('.content-area').value;
    const mainKeyword = document.querySelector('#targetKeyword').value;
    const relatedKeywords = document.querySelector('#relatedKeywords').value.split('\n');

    if (!content.trim() || !mainKeyword.trim()) {
        showToast('Please enter both content and keywords', 'warning');
        return;
    }

    showLoading();

    const analysis = {
        main: calculateKeywordMetrics(content, mainKeyword),
        related: relatedKeywords.map(keyword => calculateKeywordMetrics(content, keyword))
    };

    setTimeout(() => {
        hideLoading();
        updateKeywordAnalysis(analysis);
        createKeywordCharts(analysis);
    }, 1000);
}

// Meta Tags Analysis
function analyzeMeta() {
    const title = document.getElementById('metaTitle').value;
    const description = document.getElementById('metaDescription').value;

    if (!title.trim() && !description.trim()) {
        alert('Please enter at least one meta tag to analyze');
        return;
    }

    const titleAnalysis = analyzeMetaTitle(title);
    const descriptionAnalysis = analyzeMetaDescription(description);

    displayMetaResults(titleAnalysis, descriptionAnalysis);
}

// Utility Functions
function getWordCount(text) {
    return text.trim().split(/\s+/).length;
}

function calculateReadingTime(wordCount) {
    const wordsPerMinute = 200;
    return Math.ceil(wordCount / wordsPerMinute);
}

function calculateReadabilityScore(text) {
    // Simplified Flesch-Kincaid readability calculation
    const sentences = text.split(/[.!?]+/).length;
    const words = getWordCount(text);
    const syllables = countSyllables(text);

    if (words === 0 || sentences === 0) return 'N/A';

    const score = 206.835 - 1.015 * (words / sentences) - 84.6 * (syllables / words);
    const readabilityLevel = getReadabilityLevel(score);

    return readabilityLevel;
}

function countSyllables(text) {
    const words = text.toLowerCase().split(/\s+/);
    let count = 0;

    const vowels = ['a', 'e', 'i', 'o', 'u', 'y'];

    words.forEach(word => {
        let wordCount = 0;
        let prevChar = '';

        for (let char of word) {
            if (vowels.includes(char) && !vowels.includes(prevChar)) {
                wordCount++;
            }
            prevChar = char;
        }

        if (word.endsWith('e')) wordCount--;
        if (wordCount === 0) wordCount = 1;

        count += wordCount;
    });

    return count;
}

function getReadabilityLevel(score) {
    if (score >= 90) return 'Very Easy';
    if (score >= 80) return 'Easy';
    if (score >= 70) return 'Fairly Easy';
    if (score >= 60) return 'Standard';
    if (score >= 50) return 'Fairly Difficult';
    if (score >= 30) return 'Difficult';
    return 'Very Difficult';
}

function calculateSEOScore(content) {
    let score = 100;
    const words = getWordCount(content);

    // Length analysis
    if (words < 300) score -= 20;
    else if (words < 600) score -= 10;

    // Keyword density analysis
    const keywordDensity = calculateAverageKeywordDensity(content);
    if (keywordDensity > 3) score -= 15;
    if (keywordDensity < 0.5) score -= 10;

    // Readability analysis
    const readabilityScore = calculateReadabilityScore(content);
    if (readabilityScore === 'Very Difficult' || readabilityScore === 'Difficult') score -= 15;

    // Structure analysis
    if (!hasGoodStructure(content)) score -= 10;

    return Math.max(0, Math.min(100, score));
}

function calculateKeywordDensity(content, keyword) {
    const words = content.toLowerCase().split(/\s+/);
    const keywordCount = words.filter(word => word === keyword.toLowerCase()).length;
    const density = (keywordCount / words.length) * 100;

    return {
        count: keywordCount,
        density: density.toFixed(2),
        isOptimal: density >= 0.5 && density <= 2.5
    };
}

function hasGoodStructure(content) {
    const paragraphs = content.split('\n\n');
    const hasMultipleParagraphs = paragraphs.length > 1;
    const hasPunctuation = /[.!?]/.test(content);

    return hasMultipleParagraphs && hasPunctuation;
}

function calculateAverageKeywordDensity(content) {
    const words = content.toLowerCase().split(/\s+/);
    const wordFrequency = {};

    words.forEach(word => {
        if (word.length > 3) {
            wordFrequency[word] = (wordFrequency[word] || 0) + 1;
        }
    });

    const densities = Object.values(wordFrequency).map(count => (count / words.length) * 100);
    return densities.reduce((a, b) => a + b, 0) / densities.length;
}

function analyzeMetaTitle(title) {
    const length = title.length;
    let score = 100;
    let message = '';

    if (length === 0) {
        return {
            score: 0,
            message: 'Meta title is missing'
        };
    }

    if (length < 30) {
        score -= 30;
        message = 'Title is too short (should be 30-60 characters)';
    } else if (length > 60) {
        score -= 20;
        message = 'Title is too long (should be 30-60 characters)';
    } else {
        message = 'Title length is optimal';
    }

    // Check for keyword placement
    if (!startsWithKeyword(title)) {
        score -= 10;
        message += '. Consider starting with a key term';
    }

    return {
        score: score,
        message: message,
        length: length
    };
}

function analyzeMetaDescription(description) {
    const length = description.length;
    let score = 100;
    let message = '';

    if (length === 0) {
        return {
            score: 0,
            message: 'Meta description is missing'
        };
    }

    if (length < 120) {
        score -= 30;
        message = 'Description is too short (should be 120-155 characters)';
    } else if (length > 155) {
        score -= 20;
        message = 'Description is too long (should be 120-155 characters)';
    } else {
        message = 'Description length is optimal';
    }

    // Check for call to action
    if (!hasCallToAction(description)) {
        score -= 10;
        message += '. Consider adding a call to action';
    }

    return {
        score: score,
        message: message,
        length: length
    };
}

function startsWithKeyword(text) {
    // Simplified check - assumes important words are 4+ characters
    const words = text.split(' ');
    return words[0] && words[0].length >= 4;
}

function hasCallToAction(text) {
    const ctaPhrases = ['learn', 'discover', 'get', 'find', 'read', 'click', 'check', 'view'];
    return ctaPhrases.some(phrase => text.toLowerCase().includes(phrase));
}

// UI Update Functions
function updateKeywordAnalysis(analysis) {
    // Update main keyword results
    const mainKeywordSection = document.querySelector('.main-keyword-results');
    mainKeywordSection.innerHTML = `
        <div class="keyword-metric">
            <h4>Main Keyword</h4>
            <div class="progress-circle" data-value="${analysis.main.density}">
                <span>${analysis.main.density}%</span>
            </div>
            <p>Found ${analysis.main.count} times</p>
            <div class="optimization-status ${analysis.main.isOptimal ? 'optimal' : 'needs-improvement'}">
                ${analysis.main.isOptimal ? 'Optimal Density' : 'Needs Optimization'}
            </div>
        </div>
    `;

    // Update related keywords results
    const relatedKeywordsSection = document.querySelector('.related-keywords-results');
    relatedKeywordsSection.innerHTML = analysis.related.map(keyword => `
        <div class="keyword-metric">
            <h5>${keyword.keyword}</h5>
            <div class="progress-circle" data-value="${keyword.density}">
                <span>${keyword.density}%</span>
            </div>
            <p>Found ${keyword.count} times</p>
        </div>
    `).join('');

    // Animate progress circles
    document.querySelectorAll('.progress-circle').forEach(circle => {
        const value = circle.dataset.value;
        updateProgressCircle(circle, value);
    });
}

function displayMetaResults(titleAnalysis, descriptionAnalysis) {
    document.getElementById('metaResults').style.display = 'block';

    // Update title progress
    const titleProgress = document.getElementById('titleProgress');
    titleProgress.style.width = `${titleAnalysis.score}%`;
    titleProgress.className = `progress-bar ${getProgressBarClass(titleAnalysis.score)}`;
    document.getElementById('titleAnalysis').textContent = titleAnalysis.message;

    // Update description progress
    const descriptionProgress = document.getElementById('descriptionProgress');
    descriptionProgress.style.width = `${descriptionAnalysis.score}%`;
    descriptionProgress.className = `progress-bar ${getProgressBarClass(descriptionAnalysis.score)}`;
    document.getElementById('descriptionAnalysis').textContent = descriptionAnalysis.message;
}

function getProgressBarClass(score) {
    if (score >= 80) return 'bg-success';
    if (score >= 60) return 'bg-info';
    if (score >= 40) return 'bg-warning';
    return 'bg-danger';
}

function animateResults() {
    // Animate feature icons
    const results = document.querySelectorAll('.feature-icon');
    results.forEach((element, index) => {
        setTimeout(() => {
            element.classList.add('highlight');
            element.style.transform = 'scale(1.1)';
            setTimeout(() => {
                element.classList.remove('highlight');
                element.style.transform = 'scale(1)';
            }, 2000);
        }, index * 200);
    });

    // Animate progress bars
    document.querySelectorAll('.progress-bar').forEach(bar => {
        const target = parseFloat(bar.style.width);
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = target + '%';
            bar.style.transition = 'width 1s ease-in-out';
        }, 100);
    });

    // Animate score numbers
    document.querySelectorAll('.score-number').forEach(number => {
        const target = parseInt(number.dataset.target);
        animateValue(number.id, 0, target, 1500);
    });

    // Add fade-in animation for results sections
    document.querySelectorAll('.results-section').forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        setTimeout(() => {
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
            section.style.transition = 'all 0.5s ease-out';
        }, index * 200);
    });
}