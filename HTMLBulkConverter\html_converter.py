
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from bs4 import BeautifulSoup
import os
import threading
from pathlib import Path
import re
from datetime import datetime
import sys
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib.colors import HexColor
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

class TelegramChatConverter:
    def __init__(self, root):
        self.root = root
        self.root.title("🔄 Telegram Chat Converter - Beautiful HTML to PDF/TXT Tool")
        self.root.geometry("1000x750")
        self.root.configure(bg='#1a1a2e')

        # Variables
        self.selected_files = []
        self.output_format = tk.StringVar(value="PDF")
        self.combine_files = tk.BooleanVar(value=True)
        self.output_directory = tk.StringVar()
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="Ready to convert HTML files")

        # Style configuration
        self.setup_styles()
        self.create_widgets()

        # Set default output directory to current directory
        self.output_directory.set(os.getcwd())

    def setup_styles(self):
        """Setup modern dark theme styles"""
        style = ttk.Style()
        style.theme_use('clam')

        # Configure colors
        bg_color = '#1a1a2e'
        fg_color = '#eee2ff'
        accent_color = '#16213e'
        button_color = '#0f3460'

        style.configure('Title.TLabel',
                       background=bg_color,
                       foreground='#00d4ff',
                       font=('Segoe UI', 18, 'bold'))

        style.configure('Heading.TLabel',
                       background=bg_color,
                       foreground=fg_color,
                       font=('Segoe UI', 12, 'bold'))

        style.configure('Custom.TLabel',
                       background=bg_color,
                       foreground=fg_color,
                       font=('Segoe UI', 10))

        style.configure('Custom.TButton',
                       background=button_color,
                       foreground=fg_color,
                       font=('Segoe UI', 10, 'bold'),
                       borderwidth=2)

        style.map('Custom.TButton',
                 background=[('active', '#00d4ff')])

    def create_widgets(self):
        """Create and arrange all GUI widgets"""
        # Main container with gradient effect
        main_frame = tk.Frame(self.root, bg='#1a1a2e', padx=25, pady=25)
        main_frame.pack(fill='both', expand=True)

        # Title with emoji
        title_label = ttk.Label(main_frame,
                               text="🔄 Telegram Chat Converter",
                               style='Title.TLabel')
        title_label.pack(pady=(0, 25))

        subtitle_label = ttk.Label(main_frame,
                                  text="Convert HTML chat exports to PDF or TXT with perfect formatting",
                                  style='Custom.TLabel')
        subtitle_label.pack(pady=(0, 20))

        # File selection section
        self.create_file_selection_section(main_frame)

        # Output format section
        self.create_format_section(main_frame)

        # Output directory section
        self.create_output_section(main_frame)

        # Options section
        self.create_options_section(main_frame)

        # Progress section
        self.create_progress_section(main_frame)

        # Control buttons
        self.create_control_buttons(main_frame)

    def create_file_selection_section(self, parent):
        """Create file selection widgets"""
        # File selection frame with modern styling
        file_frame = tk.LabelFrame(parent, text="📁 Select HTML Files",
                                  bg='#16213e', fg='#eee2ff',
                                  font=('Segoe UI', 12, 'bold'),
                                  padx=20, pady=20)
        file_frame.pack(fill='x', pady=(0, 20))

        # Buttons frame
        btn_frame = tk.Frame(file_frame, bg='#16213e')
        btn_frame.pack(fill='x', pady=(0, 15))

        select_files_btn = ttk.Button(btn_frame,
                                     text="🗂️ Select HTML Files",
                                     command=self.select_files,
                                     style='Custom.TButton')
        select_files_btn.pack(side='left', padx=(0, 15))

        select_folder_btn = ttk.Button(btn_frame,
                                      text="📂 Select Folder",
                                      command=self.select_folder,
                                      style='Custom.TButton')
        select_folder_btn.pack(side='left', padx=(0, 15))

        clear_btn = ttk.Button(btn_frame,
                              text="🗑️ Clear Selection",
                              command=self.clear_selection,
                              style='Custom.TButton')
        clear_btn.pack(side='left')

        # File list with scrollbar
        list_frame = tk.Frame(file_frame, bg='#16213e')
        list_frame.pack(fill='both', expand=True)

        scrollbar = tk.Scrollbar(list_frame, bg='#0f3460')
        scrollbar.pack(side='right', fill='y')

        self.file_listbox = tk.Listbox(list_frame,
                                      yscrollcommand=scrollbar.set,
                                      bg='#0f3460',
                                      fg='#eee2ff',
                                      selectbackground='#00d4ff',
                                      selectforeground='#1a1a2e',
                                      font=('Consolas', 10),
                                      height=10)
        self.file_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.config(command=self.file_listbox.yview)

    def create_format_section(self, parent):
        """Create output format selection widgets"""
        format_frame = tk.LabelFrame(parent, text="📄 Output Format",
                                    bg='#16213e', fg='#eee2ff',
                                    font=('Segoe UI', 12, 'bold'),
                                    padx=20, pady=20)
        format_frame.pack(fill='x', pady=(0, 20))

        format_inner = tk.Frame(format_frame, bg='#16213e')
        format_inner.pack(fill='x')

        pdf_radio = tk.Radiobutton(format_inner,
                                  text="📕 PDF Format (Recommended)",
                                  variable=self.output_format,
                                  value="PDF",
                                  bg='#16213e',
                                  fg='#eee2ff',
                                  selectcolor='#0f3460',
                                  activebackground='#16213e',
                                  activeforeground='#00d4ff',
                                  font=('Segoe UI', 11))
        pdf_radio.pack(side='left', padx=(0, 40))

        txt_radio = tk.Radiobutton(format_inner,
                                  text="📝 TXT Format (Plain Text)",
                                  variable=self.output_format,
                                  value="TXT",
                                  bg='#16213e',
                                  fg='#eee2ff',
                                  selectcolor='#0f3460',
                                  activebackground='#16213e',
                                  activeforeground='#00d4ff',
                                  font=('Segoe UI', 11))
        txt_radio.pack(side='left')

    def create_output_section(self, parent):
        """Create output directory selection widgets"""
        output_frame = tk.LabelFrame(parent, text="💾 Output Directory",
                                    bg='#16213e', fg='#eee2ff',
                                    font=('Segoe UI', 12, 'bold'),
                                    padx=20, pady=20)
        output_frame.pack(fill='x', pady=(0, 20))

        output_inner = tk.Frame(output_frame, bg='#16213e')
        output_inner.pack(fill='x')

        self.output_entry = tk.Entry(output_inner,
                                    textvariable=self.output_directory,
                                    bg='#0f3460',
                                    fg='#eee2ff',
                                    font=('Segoe UI', 11),
                                    insertbackground='#eee2ff',
                                    relief='flat',
                                    bd=5)
        self.output_entry.pack(side='left', fill='x', expand=True, padx=(0, 15))

        browse_btn = ttk.Button(output_inner,
                               text="📁 Browse",
                               command=self.select_output_directory,
                               style='Custom.TButton')
        browse_btn.pack(side='right')

    def create_options_section(self, parent):
        """Create options widgets"""
        options_frame = tk.LabelFrame(parent, text="⚙️ Conversion Options",
                                     bg='#16213e', fg='#eee2ff',
                                     font=('Segoe UI', 12, 'bold'),
                                     padx=20, pady=20)
        options_frame.pack(fill='x', pady=(0, 20))

        combine_check = tk.Checkbutton(options_frame,
                                      text="🔗 Combine all files into one document",
                                      variable=self.combine_files,
                                      bg='#16213e',
                                      fg='#eee2ff',
                                      selectcolor='#0f3460',
                                      activebackground='#16213e',
                                      activeforeground='#00d4ff',
                                      font=('Segoe UI', 11))
        combine_check.pack(anchor='w')

        # Info label
        info_label = ttk.Label(options_frame,
                              text="💡 Tip: Combining files creates a single document with all conversations",
                              style='Custom.TLabel')
        info_label.pack(anchor='w', pady=(10, 0))

    def create_progress_section(self, parent):
        """Create progress widgets"""
        progress_frame = tk.LabelFrame(parent, text="📊 Progress",
                                      bg='#16213e', fg='#eee2ff',
                                      font=('Segoe UI', 12, 'bold'),
                                      padx=20, pady=20)
        progress_frame.pack(fill='x', pady=(0, 20))

        self.progress_bar = ttk.Progressbar(progress_frame,
                                           variable=self.progress_var,
                                           maximum=100,
                                           length=400,
                                           style='Custom.Horizontal.TProgressbar')
        self.progress_bar.pack(fill='x', pady=(0, 15))

        self.status_label = ttk.Label(progress_frame,
                                     textvariable=self.status_var,
                                     style='Custom.TLabel')
        self.status_label.pack(anchor='w')

    def create_control_buttons(self, parent):
        """Create control buttons"""
        button_frame = tk.Frame(parent, bg='#1a1a2e')
        button_frame.pack(fill='x', pady=(15, 0))

        convert_btn = ttk.Button(button_frame,
                                text="🚀 Start Conversion",
                                command=self.start_conversion,
                                style='Custom.TButton')
        convert_btn.pack(side='left', padx=(0, 15))

        open_output_btn = ttk.Button(button_frame,
                                    text="📂 Open Output Folder",
                                    command=self.open_output_folder,
                                    style='Custom.TButton')
        open_output_btn.pack(side='left', padx=(0, 15))

        help_btn = ttk.Button(button_frame,
                             text="❓ Help",
                             command=self.show_help,
                             style='Custom.TButton')
        help_btn.pack(side='left', padx=(0, 15))

        exit_btn = ttk.Button(button_frame,
                             text="❌ Exit",
                             command=self.root.quit,
                             style='Custom.TButton')
        exit_btn.pack(side='right')

    def select_files(self):
        """Select individual HTML files"""
        files = filedialog.askopenfilenames(
            title="Select HTML Files",
            filetypes=[("HTML files", "*.html"), ("All files", "*.*")]
        )
        if files:
            # Sort the selected files to maintain proper order
            sorted_files = sorted(files, key=lambda x: self.natural_sort_key(os.path.basename(x)))
            self.selected_files.extend(sorted_files)
            self.update_file_list()

    def select_folder(self):
        """Select folder containing HTML files"""
        folder = filedialog.askdirectory(title="Select Folder with HTML Files")
        if folder:
            html_files = []
            for file in os.listdir(folder):
                if file.lower().endswith('.html'):
                    html_files.append(os.path.join(folder, file))

            if html_files:
                # Sort files naturally (messages.html, messages2.html, etc.)
                html_files.sort(key=lambda x: self.natural_sort_key(os.path.basename(x)))
                self.selected_files.extend(html_files)
                self.update_file_list()

                # Show sorting preview
                file_order = [os.path.basename(f) for f in html_files[:10]]  # Show first 10
                order_text = "Files will be processed in this order:\n" + "\n".join(f"{i+1}. {name}" for i, name in enumerate(file_order))
                if len(html_files) > 10:
                    order_text += f"\n... and {len(html_files) - 10} more files"

                messagebox.showinfo("Success", f"Found {len(html_files)} HTML files!\n\n{order_text}")
            else:
                messagebox.showwarning("No Files", "No HTML files found in the selected folder.")

    def natural_sort_key(self, filename):
        """Natural sorting for filenames with numbers - handles messages.html, messages2.html, etc."""
        # Extract the base name without extension
        base_name = os.path.splitext(filename)[0].lower()

        # Handle Telegram export pattern: messages.html, messages2.html, messages3.html, etc.
        if base_name == 'messages':
            return (0, 1)  # messages.html is first (equivalent to messages1.html)

        # Handle messagesN.html pattern (N >= 2)
        if base_name.startswith('messages'):
            try:
                # Extract number after 'messages'
                number_part = base_name[8:]  # Remove 'messages' prefix
                if number_part.isdigit():
                    return (0, int(number_part))
            except:
                pass

        # Fallback to general natural sorting for other files
        # Split filename into parts and convert numbers to integers
        parts = re.split('([0-9]+)', filename.lower())
        result = []
        for part in parts:
            if part.isdigit():
                result.append(int(part))
            else:
                result.append(part)
        return (1, result)  # Other files come after messages files

    def clear_selection(self):
        """Clear selected files"""
        self.selected_files.clear()
        self.update_file_list()

    def update_file_list(self):
        """Update the file listbox with numbered order"""
        self.file_listbox.delete(0, tk.END)

        # Sort files to ensure proper display order
        sorted_files = sorted(self.selected_files, key=lambda x: self.natural_sort_key(os.path.basename(x)))
        self.selected_files = sorted_files

        for i, file in enumerate(self.selected_files, 1):
            filename = os.path.basename(file)
            self.file_listbox.insert(tk.END, f"{i:2d}. {filename}")

    def select_output_directory(self):
        """Select output directory"""
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_directory.set(directory)

    def open_output_folder(self):
        """Open output folder in file explorer"""
        if self.output_directory.get() and os.path.exists(self.output_directory.get()):
            if sys.platform == "win32":
                os.startfile(self.output_directory.get())
            elif sys.platform == "darwin":
                os.system(f"open '{self.output_directory.get()}'")
            else:
                os.system(f"xdg-open '{self.output_directory.get()}'")
        else:
            messagebox.showwarning("Warning", "Output directory not set or doesn't exist.")

    def show_help(self):
        """Show help dialog"""
        help_text = """
🔄 Telegram Chat Converter Help

This tool converts Telegram HTML chat exports to PDF or TXT format.

📁 How to use:
1. Select HTML files or a folder containing HTML files
2. Choose output format (PDF recommended for better formatting)
3. Select output directory
4. Choose whether to combine files into one document
5. Click 'Start Conversion'

📝 Features:
• Preserves all text content from chat exports
• Maintains message structure and timestamps
• Supports bulk conversion
• Can combine multiple files into one document
• Beautiful formatting for PDF output

💡 Tips:
• PDF format preserves better formatting
• Use 'Combine files' for a single document
• Files are processed in chronological order
• All emojis and special characters are preserved

🔧 Requirements:
• Python with tkinter, BeautifulSoup4
• For PDF: reportlab library
• For combining PDFs: PyPDF2 library
        """

        help_window = tk.Toplevel(self.root)
        help_window.title("Help - Telegram Chat Converter")
        help_window.geometry("600x500")
        help_window.configure(bg='#1a1a2e')

        text_widget = tk.Text(help_window,
                             bg='#16213e',
                             fg='#eee2ff',
                             font=('Segoe UI', 10),
                             wrap='word',
                             padx=20,
                             pady=20)
        text_widget.pack(fill='both', expand=True, padx=20, pady=20)
        text_widget.insert('1.0', help_text)
        text_widget.config(state='disabled')

    def start_conversion(self):
        """Start the conversion process"""
        if not self.selected_files:
            messagebox.showwarning("Warning", "Please select HTML files to convert.")
            return

        if not self.output_directory.get():
            messagebox.showwarning("Warning", "Please select an output directory.")
            return

        # Check if output directory exists
        if not os.path.exists(self.output_directory.get()):
            try:
                os.makedirs(self.output_directory.get())
            except Exception as e:
                messagebox.showerror("Error", f"Could not create output directory: {str(e)}")
                return

        # Start conversion in a separate thread
        threading.Thread(target=self.convert_files, daemon=True).start()

    def convert_files(self):
        """Convert files in background thread"""
        try:
            self.status_var.set("Starting conversion...")
            self.progress_var.set(0)

            output_format = self.output_format.get().lower()
            combine = self.combine_files.get()

            if combine:
                self.convert_combined(output_format)
            else:
                self.convert_individual(output_format)

            self.status_var.set("Conversion completed successfully!")
            self.progress_var.set(100)
            messagebox.showinfo("Success", "Files have been converted successfully!")

        except Exception as e:
            self.status_var.set("Conversion failed!")
            messagebox.showerror("Error", f"Conversion failed: {str(e)}")

    def convert_individual(self, output_format):
        """Convert files individually"""
        total_files = len(self.selected_files)

        for i, html_file in enumerate(self.selected_files):
            self.status_var.set(f"Converting {os.path.basename(html_file)}...")

            # Generate output filename
            base_name = os.path.splitext(os.path.basename(html_file))[0]
            output_file = os.path.join(self.output_directory.get(), f"{base_name}.{output_format}")

            if output_format == "pdf":
                self.convert_to_pdf(html_file, output_file)
            else:
                self.convert_to_txt(html_file, output_file)

            # Update progress
            progress = ((i + 1) / total_files) * 100
            self.progress_var.set(progress)
            self.root.update_idletasks()

    def convert_combined(self, output_format):
        """Convert and combine all files"""
        self.status_var.set("Extracting content from all files...")

        all_content = []
        total_files = len(self.selected_files)

        # Extract content from all files
        for i, html_file in enumerate(self.selected_files):
            self.status_var.set(f"Processing {os.path.basename(html_file)}...")

            content = self.extract_chat_content(html_file)
            if content:
                all_content.append({
                    'filename': os.path.basename(html_file),
                    'content': content
                })

            # Update progress for extraction phase (0-50%)
            progress = ((i + 1) / total_files) * 50
            self.progress_var.set(progress)
            self.root.update_idletasks()

        # Generate combined output
        self.status_var.set("Creating combined document...")
        output_file = os.path.join(self.output_directory.get(), f"combined_chat.{output_format}")

        if output_format == "pdf":
            self.create_combined_pdf(all_content, output_file)
        else:
            self.create_combined_txt(all_content, output_file)

        self.progress_var.set(100)

    def extract_chat_content(self, html_file):
        """Extract chat content from HTML file"""
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                soup = BeautifulSoup(f, 'html.parser')

            # Extract chat title
            title_elem = soup.find('div', class_='text bold')
            chat_title = title_elem.get_text().strip() if title_elem else "Chat"

            # Extract messages
            messages = []
            message_divs = soup.find_all('div', class_='message')

            for msg_div in message_divs:
                # Skip service messages
                if 'service' in msg_div.get('class', []):
                    date_elem = msg_div.find('div', class_='body details')
                    if date_elem:
                        messages.append({
                            'type': 'service',
                            'content': date_elem.get_text().strip(),
                            'date': '',
                            'sender': ''
                        })
                    continue

                # Extract message details
                body = msg_div.find('div', class_='body')
                if not body:
                    continue

                # Get date
                date_elem = body.find('div', class_='date details')
                date = date_elem.get_text().strip() if date_elem else ""

                # Get sender
                sender_elem = body.find('div', class_='from_name')
                sender = sender_elem.get_text().strip() if sender_elem else ""

                # Get message text
                text_elem = body.find('div', class_='text')
                text = text_elem.get_text().strip() if text_elem else ""

                # Get reply info if exists
                reply_elem = body.find('div', class_='reply_to')
                reply_info = reply_elem.get_text().strip() if reply_elem else ""

                if text or reply_info:
                    messages.append({
                        'type': 'message',
                        'content': text,
                        'reply': reply_info,
                        'date': date,
                        'sender': sender
                    })

            return {
                'title': chat_title,
                'messages': messages
            }

        except Exception as e:
            print(f"Error extracting content from {html_file}: {str(e)}")
            return None

    def convert_to_txt(self, html_file, output_file):
        """Convert single HTML file to TXT"""
        content = self.extract_chat_content(html_file)
        if not content:
            return

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"Chat: {content['title']}\n")
            f.write("=" * 50 + "\n\n")

            for msg in content['messages']:
                if msg['type'] == 'service':
                    f.write(f"\n--- {msg['content']} ---\n\n")
                else:
                    if msg['sender']:
                        f.write(f"[{msg['date']}] {msg['sender']}:\n")
                    else:
                        f.write(f"[{msg['date']}]\n")

                    if msg['reply']:
                        f.write(f"  >> {msg['reply']}\n")

                    f.write(f"{msg['content']}\n\n")

    def convert_to_pdf(self, html_file, output_file):
        """Convert single HTML file to PDF"""
        if not REPORTLAB_AVAILABLE:
            messagebox.showerror("Error", "ReportLab library is required for PDF conversion. Please install it with: pip install reportlab")
            return

        content = self.extract_chat_content(html_file)
        if not content:
            return

        doc = SimpleDocTemplate(output_file, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []

        # Title style
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=30,
            textColor=HexColor('#1a1a2e')
        )

        # Message styles
        sender_style = ParagraphStyle(
            'Sender',
            parent=styles['Normal'],
            fontSize=10,
            textColor=HexColor('#0f3460'),
            fontName='Helvetica-Bold'
        )

        message_style = ParagraphStyle(
            'Message',
            parent=styles['Normal'],
            fontSize=10,
            spaceAfter=12,
            leftIndent=20
        )

        service_style = ParagraphStyle(
            'Service',
            parent=styles['Normal'],
            fontSize=9,
            textColor=HexColor('#666666'),
            alignment=1,  # Center
            spaceAfter=20
        )

        # Add title
        story.append(Paragraph(f"Chat: {content['title']}", title_style))
        story.append(Spacer(1, 20))

        # Add messages
        for msg in content['messages']:
            if msg['type'] == 'service':
                story.append(Paragraph(f"— {msg['content']} —", service_style))
            else:
                if msg['sender']:
                    story.append(Paragraph(f"[{msg['date']}] {msg['sender']}:", sender_style))
                else:
                    story.append(Paragraph(f"[{msg['date']}]", sender_style))

                if msg['reply']:
                    reply_style = ParagraphStyle(
                        'Reply',
                        parent=styles['Normal'],
                        fontSize=9,
                        textColor=HexColor('#888888'),
                        leftIndent=40,
                        fontName='Helvetica-Oblique'
                    )
                    story.append(Paragraph(f"↳ {msg['reply']}", reply_style))

                story.append(Paragraph(msg['content'], message_style))

        doc.build(story)

    def create_combined_txt(self, all_content, output_file):
        """Create combined TXT file"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("COMBINED TELEGRAM CHAT EXPORT\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total files: {len(all_content)}\n")
            f.write(f"Processing order: messages.html → messages2.html → messages3.html → ...\n\n")

            for i, file_content in enumerate(all_content):
                f.write(f"\n{'='*80}\n")
                f.write(f"FILE {i+1} OF {len(all_content)}: {file_content['filename']}\n")
                f.write(f"{'='*80}\n\n")

                content = file_content['content']
                f.write(f"Chat: {content['title']}\n")
                f.write("-" * 50 + "\n\n")

                for msg in content['messages']:
                    if msg['type'] == 'service':
                        f.write(f"\n--- {msg['content']} ---\n\n")
                    else:
                        if msg['sender']:
                            f.write(f"[{msg['date']}] {msg['sender']}:\n")
                        else:
                            f.write(f"[{msg['date']}]\n")

                        if msg['reply']:
                            f.write(f"  >> {msg['reply']}\n")

                        f.write(f"{msg['content']}\n\n")

    def create_combined_pdf(self, all_content, output_file):
        """Create combined PDF file"""
        if not REPORTLAB_AVAILABLE:
            messagebox.showerror("Error", "ReportLab library is required for PDF conversion. Please install it with: pip install reportlab")
            return

        doc = SimpleDocTemplate(output_file, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []

        # Define styles
        main_title_style = ParagraphStyle(
            'MainTitle',
            parent=styles['Title'],
            fontSize=20,
            spaceAfter=30,
            textColor=HexColor('#1a1a2e'),
            alignment=1  # Center
        )

        file_title_style = ParagraphStyle(
            'FileTitle',
            parent=styles['Heading1'],
            fontSize=14,
            spaceAfter=20,
            textColor=HexColor('#0f3460'),
            borderWidth=1,
            borderColor=HexColor('#0f3460'),
            borderPadding=10
        )

        # Add main title
        story.append(Paragraph("COMBINED TELEGRAM CHAT EXPORT", main_title_style))
        story.append(Spacer(1, 20))
        story.append(Paragraph(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
        story.append(Paragraph(f"Total files: {len(all_content)}", styles['Normal']))
        story.append(Paragraph("Processing order: messages.html → messages2.html → messages3.html → ...", styles['Normal']))
        story.append(Spacer(1, 30))

        # Add each file's content
        for i, file_content in enumerate(all_content):
            if i > 0:
                story.append(PageBreak())

            story.append(Paragraph(f"FILE {i+1} OF {len(all_content)}: {file_content['filename']}", file_title_style))

            content = file_content['content']
            story.append(Paragraph(f"Chat: {content['title']}", styles['Heading2']))
            story.append(Spacer(1, 20))

            # Add messages with same styling as individual PDF
            for msg in content['messages']:
                if msg['type'] == 'service':
                    service_style = ParagraphStyle(
                        'Service',
                        parent=styles['Normal'],
                        fontSize=9,
                        textColor=HexColor('#666666'),
                        alignment=1,
                        spaceAfter=20
                    )
                    story.append(Paragraph(f"— {msg['content']} —", service_style))
                else:
                    sender_style = ParagraphStyle(
                        'Sender',
                        parent=styles['Normal'],
                        fontSize=10,
                        textColor=HexColor('#0f3460'),
                        fontName='Helvetica-Bold'
                    )

                    message_style = ParagraphStyle(
                        'Message',
                        parent=styles['Normal'],
                        fontSize=10,
                        spaceAfter=12,
                        leftIndent=20
                    )

                    if msg['sender']:
                        story.append(Paragraph(f"[{msg['date']}] {msg['sender']}:", sender_style))
                    else:
                        story.append(Paragraph(f"[{msg['date']}]", sender_style))

                    if msg['reply']:
                        reply_style = ParagraphStyle(
                            'Reply',
                            parent=styles['Normal'],
                            fontSize=9,
                            textColor=HexColor('#888888'),
                            leftIndent=40,
                            fontName='Helvetica-Oblique'
                        )
                        story.append(Paragraph(f"↳ {msg['reply']}", reply_style))

                    story.append(Paragraph(msg['content'], message_style))

        doc.build(story)

def main():
    root = tk.Tk()
    app = TelegramChatConverter(root)
    root.mainloop()

if __name__ == "__main__":
    main()
