<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>HTML to PDF Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-form {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        .file-input {
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            width: 100%;
            box-sizing: border-box;
        }
        .file-input:hover {
            border-color: #666;
        }
        .convert-button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .convert-button:hover {
            background-color: #45a049;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .error {
            background-color: #f2dede;
            color: #a94442;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>HTML to PDF Converter</h1>
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="status error">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        <form class="upload-form" method="post" enctype="multipart/form-data">
            <div class="file-input">
                <input type="file" name="files" multiple accept=".html" />
                <p>Select one or more HTML files</p>
            </div>
            <button type="submit" class="convert-button">Convert and Merge PDFs</button>
        </form>
        <div id="status" class="status" style="display: none;"></div>
    </div>
    <script>
        document.querySelector('form').onsubmit = function() {
            document.getElementById('status').style.display = 'block';
            document.getElementById('status').innerHTML = 'Converting files... Please wait.';
            document.querySelector('.convert-button').disabled = true;
        };
    </script>
</body>
</html>
