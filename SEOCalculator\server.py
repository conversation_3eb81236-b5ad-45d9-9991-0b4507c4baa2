import http.server
import socketserver
import webbrowser
import os

PORT = 8081

class Handler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        super().end_headers()

def start_server():
    # Ensure we're in the correct directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)

    print(f"Starting server in: {os.getcwd()}")
    print(f"Server URL: http://localhost:{PORT}")

    try:
        with socketserver.TCPServer(("", PORT), Handler) as httpd:
            print(f"Server started successfully at http://localhost:{PORT}")
            print("Opening browser...")
            webbrowser.open(f'http://localhost:{PORT}/index.html')
            print("Press Ctrl+C to stop the server")
            httpd.serve_forever()
    except OSError as e:
        if e.errno == 10048:  # Port already in use
            print(f"Error: Port {PORT} is already in use.")
            print("Please close any other applications using this port and try again.")
        else:
            print(f"Error starting server: {e}")
        input("Press Enter to exit...")
    except KeyboardInterrupt:
        print("\nServer stopped by user.")

if __name__ == '__main__':
    start_server()